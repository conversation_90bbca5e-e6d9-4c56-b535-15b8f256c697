"""
Phone call routes for AI Therapist
"""
import logging
from typing import List
from fastapi import APIRouter, HTTPException
from backend.models.schemas import PhoneConfigResponse, CallLogEntry
from backend.utils.session_manager import get_call_logs, get_phone_number, add_call_log, update_call_log

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/phone/config", response_model=PhoneConfigResponse)
async def get_phone_config():
    """Get phone configuration and call logs"""
    try:
        call_logs = get_call_logs()
        phone_number = get_phone_number()
        
        # Count active calls
        active_calls = len([call for call in call_logs if call.status in ["incoming", "answered"]])
        
        return PhoneConfigResponse(
            phone_number=phone_number,
            call_logs=call_logs,
            total_calls=len(call_logs),
            active_calls=active_calls
        )
    except Exception as e:
        logger.error(f"Error getting phone config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get phone config: {str(e)}")

@router.get("/phone/logs", response_model=List[CallLogEntry])
async def get_phone_logs():
    """Get all phone call logs"""
    try:
        return get_call_logs()
    except Exception as e:
        logger.error(f"Error getting phone logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get phone logs: {str(e)}")

@router.post("/phone/incoming")
async def handle_incoming_call(phone_number: str):
    """Handle incoming phone call"""
    try:
        # Log the incoming call
        call_entry = add_call_log(phone_number, "incoming")
        logger.info(f"Incoming call from {phone_number}, logged as {call_entry.id}")
        
        return {
            "status": "success",
            "message": "Incoming call logged",
            "call_id": call_entry.id,
            "phone_number": phone_number
        }
    except Exception as e:
        logger.error(f"Error handling incoming call: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to handle incoming call: {str(e)}")

@router.post("/phone/answer")
async def answer_call(call_id: str):
    """Answer a phone call"""
    try:
        success = update_call_log(call_id, "answered")
        if success:
            logger.info(f"Call {call_id} answered")
            return {
                "status": "success",
                "message": "Call answered",
                "call_id": call_id
            }
        else:
            raise HTTPException(status_code=404, detail="Call not found")
    except Exception as e:
        logger.error(f"Error answering call: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to answer call: {str(e)}")

@router.post("/phone/end")
async def end_call(call_id: str, duration: int = None):
    """End a phone call"""
    try:
        success = update_call_log(call_id, "ended", duration)
        if success:
            logger.info(f"Call {call_id} ended with duration {duration} seconds")
            return {
                "status": "success",
                "message": "Call ended",
                "call_id": call_id,
                "duration": duration
            }
        else:
            raise HTTPException(status_code=404, detail="Call not found")
    except Exception as e:
        logger.error(f"Error ending call: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to end call: {str(e)}")

@router.delete("/phone/logs")
async def clear_call_logs():
    """Clear all call logs"""
    try:
        # This would clear the call logs - for now just return success
        # In a real implementation, you'd clear the call_logs list
        logger.info("Call logs cleared")
        return {
            "status": "success",
            "message": "Call logs cleared"
        }
    except Exception as e:
        logger.error(f"Error clearing call logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear call logs: {str(e)}")
