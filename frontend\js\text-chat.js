/**
 * Text Chat Functionality for AI Therapist
 */

let websocket = null;

function setupTextChat() {
    if (!userInput || !sendButton) return;

    // Set up WebSocket connection
    connectWebSocket();

    // Send button click
    sendButton.addEventListener('click', sendMessage);

    // Enter key to send
    userInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Auto-resize textarea
    userInput.addEventListener('input', () => {
        userInput.style.height = 'auto';
        userInput.style.height = userInput.scrollHeight + 'px';
    });
}

function initializeTextMode() {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
        connectWebSocket();
    }
}

function connectWebSocket() {
    try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        websocket = new WebSocket(wsUrl);

        websocket.onopen = () => {
            console.log('WebSocket connected');
            showStatus('Connected to AI Therapist');
        };

        websocket.onmessage = (event) => {
            const aiResponse = event.data;
            addMessage(aiResponse, 'ai');
        };

        websocket.onclose = () => {
            console.log('WebSocket disconnected');
            showStatus('Disconnected from AI Therapist');
            
            // Attempt to reconnect after 3 seconds
            setTimeout(() => {
                if (currentMode === 'text') {
                    connectWebSocket();
                }
            }, 3000);
        };

        websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            showToast('Connection error. Please refresh the page.', 'error');
        };

    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
        showToast('Failed to connect to chat service', 'error');
    }
}

function sendMessage() {
    if (!userInput || !websocket) return;

    const message = userInput.value.trim();
    if (!message) return;

    // Add user message to chat
    addMessage(message, 'user');

    // Clear input
    userInput.value = '';
    userInput.style.height = 'auto';

    // Send to WebSocket
    if (websocket.readyState === WebSocket.OPEN) {
        websocket.send(message);
    } else {
        showToast('Not connected to chat service', 'error');
        connectWebSocket();
    }
}

function addMessage(text, sender) {
    if (!messagesContainer) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.textContent = sender === 'user' ? '👤' : '🤖';

    const bubble = document.createElement('div');
    bubble.className = 'bubble';
    
    const p = document.createElement('p');
    p.textContent = text;
    bubble.appendChild(p);

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(bubble);

    messagesContainer.appendChild(messageDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Microphone button for text chat (speech-to-text)
function setupTextChatMicrophone() {
    if (!micButton) return;

    let recognition = null;
    
    // Check for speech recognition support
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';

        recognition.onstart = () => {
            micButton.classList.add('active');
            showStatus('Listening...');
        };

        recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            if (userInput) {
                userInput.value = transcript;
                userInput.focus();
            }
        };

        recognition.onend = () => {
            micButton.classList.remove('active');
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            micButton.classList.remove('active');
            showToast('Speech recognition failed', 'error');
        };

        micButton.addEventListener('click', () => {
            if (micButton.classList.contains('active')) {
                recognition.stop();
            } else {
                recognition.start();
            }
        });
    } else {
        // Hide microphone button if not supported
        micButton.style.display = 'none';
    }
}
