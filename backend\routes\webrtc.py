"""
WebRTC routes for AI Therapist
"""
import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Depends
from sqlalchemy.orm import Session
from backend.models.schemas import InputData, FileUploadResponse, SessionEndRequest
from backend.services.webrtc_service import is_webrtc_available
from backend.utils.session_manager import get_all_sessions, remove_session, set_session_user
from backend.utils.audio_utils import encode_file_content
from backend.models.database import get_db, User
from backend.routes.auth import get_authenticated_user
from PIL import Image
from io import BytesIO

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/upload_file", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """Upload file to share with AI Therapist"""
    try:
        # Read file content
        file_content = await file.read()
        
        # Determine mime type
        mime_type = file.content_type or "application/octet-stream"
        
        # For images, convert to JPEG if needed
        if mime_type.startswith("image/"):
            try:
                pil_image = Image.open(BytesIO(file_content))
                with Bytes<PERSON>() as output_bytes:
                    pil_image.save(output_bytes, "JPEG")
                    file_content = output_bytes.getvalue()
                mime_type = "image/jpeg"
            except Exception as img_error:
                logger.warning(f"Could not process image: {img_error}")
        
        # Encode file for Gemini
        encoded_file = encode_file_content(file_content, mime_type)
        
        logger.info(f"File uploaded: {file.filename}, type: {mime_type}, size: {len(file_content)} bytes")
        
        return FileUploadResponse(
            status="success",
            filename=file.filename,
            mime_type=mime_type,
            size=len(file_content),
            encoded_data=encoded_file
        )
        
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@router.post("/input_hook")
async def set_input_hook(
    body: InputData,
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Set input parameters for WebRTC stream with validation"""
    try:
        if not is_webrtc_available():
            return {
                "status": "error",
                "message": "WebRTC features are not available in this deployment",
                "webrtc_id": body.webrtc_id,
                "voice": body.voice_name,
                "mode": body.mode
            }
        
        # Validate inputs
        if not body.webrtc_id or not body.voice_name:
            raise HTTPException(status_code=400, detail="Missing webrtc_id or voice_name")
        
        # Validate voice name
        valid_voices = ["Puck", "Charon", "Kore", "Fenrir", "Aoede"]
        if body.voice_name not in valid_voices:
            logger.warning(f"Invalid voice {body.voice_name}, using Aoede")
            body.voice_name = "Aoede"
        
        # Note: Stream input setting would be handled by the main app
        # This is just the validation endpoint
            
        logger.info(f"Input validated for WebRTC ID: {body.webrtc_id}, Voice: {body.voice_name}, Mode: {body.mode}")
        return {"status": "ok", "webrtc_id": body.webrtc_id, "voice": body.voice_name, "mode": body.mode}
        
    except Exception as e:
        logger.error(f"Error setting input: {e}")
        raise HTTPException(status_code=500, detail=f"Input hook failed: {str(e)}")

@router.post("/end_session")
async def end_session(request: SessionEndRequest):
    """Manually end a session"""
    try:
        session_id = request.session_id if request else None
        
        if session_id:
            session_info = get_all_sessions().get(session_id)
            if session_info:
                handler = session_info["handler"]
                if hasattr(handler, "shutdown"):
                    handler.shutdown()
                remove_session(session_id)
                logger.info(f"Session {session_id} ended manually")
                return {"status": "success", "message": "Session ended"}
            else:
                return {"status": "error", "message": "Session not found"}
        else:
            # End all active sessions
            sessions = get_all_sessions()
            for sid, session_info in sessions.items():
                handler = session_info["handler"]
                if hasattr(handler, "shutdown"):
                    handler.shutdown()
                remove_session(sid)
            logger.info("All sessions ended")
            return {"status": "success", "message": "All sessions ended"}
            
    except Exception as e:
        logger.error(f"Error ending session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to end session: {str(e)}")
