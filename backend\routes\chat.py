"""
Chat routes for AI Therapist
"""
import logging
from fastapi import <PERSON><PERSON>out<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from backend.models.schemas import Chat<PERSON>equest, ChatResponse
from backend.config.settings import SYSTEM_PROMPT
from backend.services.gemini_service import GeminiService
from backend.utils.audio_utils import detect_goodbye

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Text-based chat endpoint using Gemini"""
    try:
        # Check for goodbye in text chat
        if detect_goodbye(request.prompt):
            return ChatResponse(response="Thank you for sharing with me today. Take care of yourself, and remember that you have the strength to handle whatever comes your way. Feel free to come back anytime you need support. Goodbye for now! 💙")
        
        # Initialize Gemini service
        gemini_service = GeminiService()
        
        # Generate response
        ai_response = await gemini_service.generate_text_response(
            request.prompt, 
            SYSTEM_PROMPT
        )
        
        logger.info(f"Text chat response generated: {len(ai_response)} characters")
        return ChatResponse(response=ai_response)

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        
        # Fallback response for therapeutic context
        fallback_response = (
            "I'm experiencing a technical difficulty right now. "
            "Your feelings and thoughts are important - would you like to try again, "
            "or would it help to contact a human therapist?"
        )
        return ChatResponse(response=fallback_response)

@router.websocket("/ws")
async def websocket_text_chat(websocket: WebSocket):
    """WebSocket endpoint for text-based chat with streaming responses"""
    await websocket.accept()
    logger.info("WebSocket connection established for text chat")
    
    try:
        gemini_service = GeminiService()
        
        while True:
            # Receive message from client
            user_msg = await websocket.receive_text()
            logger.info(f"Received WebSocket message: {user_msg[:100]}...")

            # Crisis detection keywords
            crisis_indicators = [
                "suicide", "kill myself", "self-harm", "end it all", 
                "hurt myself", "don't want to live", "better off dead"
            ]
            
            if any(indicator in user_msg.lower() for indicator in crisis_indicators):
                crisis_response = (
                    "🚨 I'm very concerned about what you're sharing. "
                    "Please reach out for immediate support:\n\n"
                    "• National Suicide Prevention Lifeline: 988\n"
                    "• Crisis Text Line: Text HOME to 741741\n"
                    "• Emergency Services: 911\n\n"
                    "Your life has value and there are people who want to help."
                )
                await websocket.send_text(crisis_response)
                continue

            # Check for goodbye
            if detect_goodbye(user_msg):
                goodbye_response = (
                    "Thank you for sharing with me today. Take care of yourself, and remember "
                    "that you have the strength to handle whatever comes your way. Feel free to "
                    "come back anytime you need support. Goodbye for now! 💙"
                )
                await websocket.send_text(goodbye_response)
                continue

            try:
                # Generate response
                response_text = await gemini_service.generate_text_response(
                    user_msg,
                    SYSTEM_PROMPT
                )

                # Clean up the response text
                if response_text:
                    # Remove common voice artifacts
                    artifacts_to_remove = [
                        "[pause]", "...", "… [pause] …", "[PAUSE]",
                        "*pause*", "(pause)", "... [pause] ...",
                        "[breath]", "*breath*", "(breath)"
                    ]
                    
                    for artifact in artifacts_to_remove:
                        response_text = response_text.replace(artifact, "")
                    
                    # Clean up extra spaces and line breaks
                    clean_text = " ".join(response_text.split())
                    
                    await websocket.send_text(clean_text)
                else:
                    await websocket.send_text("I apologize, I couldn't generate a proper response. How else can I help you?")

            except Exception as e:
                logger.error(f"Error generating response: {e}")
                await websocket.send_text(
                    "I apologize, but I'm having technical difficulties. "
                    "Your mental health is important - please consider speaking "
                    "with a human therapist if you need immediate support."
                )

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.send_text(f"Connection error occurred: {str(e)}")
        except:
            pass
