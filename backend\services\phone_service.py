"""
Phone integration service for AI Therapist
"""
import logging
import async<PERSON>
from typing import Optional
from backend.config.settings import get_settings
from backend.services.webrtc_service import FASTRTC_AVAILABLE
from backend.utils.session_manager import add_call_log, get_phone_number

logger = logging.getLogger(__name__)

class PhoneService:
    """Service for phone integration using FastPhone"""
    
    def __init__(self):
        self.settings = get_settings()
        self.phone_stream = None
        self.phone_number = None
        self.access_code = None
        
    async def initialize_phone_service(self):
        """Initialize phone service with FastPhone integration"""
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, phone service disabled")
            return False
            
        try:
            from fastrtc import Stream
            from backend.handlers.phone_handler import PhoneHandler
            
            # Create phone stream
            self.phone_stream = Stream(
                modality="audio",
                mode="receive-only",  # Phone calls are receive-only
                handler=PhoneHandler(),
                concurrency_limit=10,  # Allow multiple phone calls
                time_limit=1800,  # 30 minutes max per call
            )
            
            # Get phone credentials
            phone_info = await self._get_phone_credentials()
            if phone_info:
                self.phone_number = phone_info.get('phone_number')
                self.access_code = phone_info.get('access_code')
                logger.info(f"Phone service initialized: {self.phone_number} (Code: {self.access_code})")
                return True
            else:
                logger.error("Failed to get phone credentials")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing phone service: {e}")
            return False
    
    async def _get_phone_credentials(self):
        """Get phone credentials from FastPhone"""
        try:
            # This would typically come from FastPhone registration
            # For now, return the example from your logs
            return {
                'phone_number': '******-713-4471',
                'access_code': '163760'
            }
        except Exception as e:
            logger.error(f"Error getting phone credentials: {e}")
            return None
    
    def get_phone_info(self):
        """Get current phone information"""
        return {
            'phone_number': self.phone_number or get_phone_number(),
            'access_code': self.access_code,
            'available': FASTRTC_AVAILABLE and self.phone_stream is not None
        }
    
    async def handle_incoming_call(self, caller_number: str):
        """Handle incoming phone call"""
        try:
            # Log the incoming call
            call_entry = add_call_log(caller_number, "incoming")
            logger.info(f"Incoming call from {caller_number}, logged as {call_entry.id}")
            
            # Here you would typically handle the call routing
            # For now, just log it
            return call_entry
            
        except Exception as e:
            logger.error(f"Error handling incoming call: {e}")
            return None

# Global phone service instance
phone_service = PhoneService()
