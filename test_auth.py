#!/usr/bin/env python3
"""Test script to verify authentication"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_login(email, password):
    """Test login with given credentials"""
    print(f"\n🔐 Testing login for: {email}")
    
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": email,
        "password": password
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Login successful!")
        print(f"   User: {data['user']['email']}")
        print(f"   Username: {data['user']['username']}")
        print(f"   Full Name: {data['user']['full_name']}")
        print(f"   Token: {data['access_token'][:20]}...")
        
        # Test /auth/me endpoint
        headers = {"Authorization": f"Bearer {data['access_token']}"}
        me_response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        
        if me_response.status_code == 200:
            me_data = me_response.json()
            print(f"✅ /auth/me successful!")
            print(f"   User: {me_data['email']}")
            print(f"   Username: {me_data['username']}")
            print(f"   Full Name: {me_data['full_name']}")
        else:
            print(f"❌ /auth/me failed: {me_response.status_code}")
            
        return data['access_token']
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return None

def test_usage(token):
    """Test usage endpoint"""
    print(f"\n📊 Testing usage endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/payments/usage", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Usage endpoint successful!")
        print(f"   Plan: {data.get('plan_type', 'Unknown')}")
        print(f"   Minutes: {data.get('minutes_used', 0)}/{data.get('minutes_limit', 'Unlimited')}")
    else:
        print(f"❌ Usage endpoint failed: {response.status_code}")
        print(f"   Error: {response.text}")

if __name__ == "__main__":
    # Test with different users
    test_users = [
        ("<EMAIL>", "password123"),
        ("<EMAIL>", "password123"),
        ("<EMAIL>", "password123")
    ]
    
    for email, password in test_users:
        token = test_login(email, password)
        if token:
            test_usage(token)
        print("-" * 50)
