"""
Email service for AI Therapist
"""
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Optional
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending emails"""
    
    def __init__(self):
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.email = "<EMAIL>"
        self.password = "lceg dmyy fvwm fkor"  # App password
        
    def send_email(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None) -> bool:
        """Send an email"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add text version if provided
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)
            
            # Add HTML version
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email, self.password)
                server.send_message(msg)
                
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def send_welcome_email(self, user_email: str, user_name: str) -> bool:
        """Send welcome email to new user"""
        subject = "Welcome to AI Therapist - Your Mental Health Journey Starts Here! 🧠"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to AI Therapist</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #0f172a; color: #e2e8f0; }}
                .container {{ max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #1e293b 0%, #334155 100%); }}
                .header {{ background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }}
                .logo {{ width: 80px; height: 80px; margin: 0 auto 1rem; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; }}
                .content {{ padding: 2rem; }}
                .feature {{ background: rgba(99, 102, 241, 0.1); border-left: 4px solid #6366f1; padding: 1rem; margin: 1rem 0; border-radius: 0.5rem; }}
                .cta {{ text-align: center; margin: 2rem 0; }}
                .btn {{ display: inline-block; background: #6366f1; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 0.5rem; font-weight: bold; }}
                .footer {{ background: #0f172a; padding: 1rem; text-align: center; font-size: 0.9rem; color: #94a3b8; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">🧠</div>
                    <h1 style="margin: 0; color: white;">Welcome to AI Therapist!</h1>
                    <p style="margin: 0.5rem 0 0 0; color: rgba(255,255,255,0.9);">Your 24/7 Mental Health Companion</p>
                </div>
                
                <div class="content">
                    <h2 style="color: #6366f1;">Hello {user_name}! 👋</h2>
                    
                    <p>Thank you for joining AI Therapist! We're excited to support you on your mental health journey. Our AI-powered therapeutic companion is here to provide you with compassionate, professional support whenever you need it.</p>
                    
                    <div class="feature">
                        <h3 style="margin-top: 0; color: #6366f1;">🎤 Voice Therapy</h3>
                        <p>Have natural conversations with our AI therapist using real-time voice chat with 5 therapeutic voices.</p>
                    </div>
                    
                    <div class="feature">
                        <h3 style="margin-top: 0; color: #6366f1;">📹 Video Sessions</h3>
                        <p>Share documents and have face-to-face sessions where our AI can see and respond to your emotional cues.</p>
                    </div>
                    
                    <div class="feature">
                        <h3 style="margin-top: 0; color: #6366f1;">📞 Phone Support</h3>
                        <p>Call directly from any phone for immediate support - no app required!</p>
                    </div>
                    
                    <div class="feature">
                        <h3 style="margin-top: 0; color: #6366f1;">💬 Text Chat</h3>
                        <p>Private, secure text conversations with streaming responses and full context memory.</p>
                    </div>
                    
                    <div class="cta">
                        <a href="http://localhost:8000/app" class="btn">Start Your First Session</a>
                    </div>
                    
                    <p><strong>Important:</strong> AI Therapist is designed to complement, not replace, professional mental health services. If you're experiencing a crisis, please contact emergency services or a mental health helpline immediately.</p>
                    
                    <p>We're here to support you 24/7. Welcome to your mental health journey!</p>
                    
                    <p>Best regards,<br>The AI Therapist Team</p>
                </div>
                
                <div class="footer">
                    <p>© 2025 AI Therapist | Your privacy is our priority</p>
                    <p>This email was sent to {user_email}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Welcome to AI Therapist, {user_name}!
        
        Thank you for joining our mental health platform. We're excited to support you on your journey.
        
        Features available to you:
        - Voice Therapy: Natural conversations with AI
        - Video Sessions: Face-to-face support with document sharing
        - Phone Support: Call directly for immediate help
        - Text Chat: Private, secure messaging
        
        Start your first session: http://localhost:8000/app
        
        Important: AI Therapist complements professional mental health services. For crisis situations, contact emergency services.
        
        Best regards,
        The AI Therapist Team
        """
        
        return self.send_email(user_email, subject, html_content, text_content)
    
    def send_subscription_email(self, user_email: str, user_name: str, plan_name: str, plan_features: list) -> bool:
        """Send subscription confirmation email"""
        subject = f"Welcome to {plan_name} Plan - AI Therapist 🎉"
        
        features_html = "".join([f"<li>✓ {feature}</li>" for feature in plan_features])
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Subscription Confirmed</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #0f172a; color: #e2e8f0; }}
                .container {{ max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #1e293b 0%, #334155 100%); }}
                .header {{ background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 2rem; text-align: center; }}
                .content {{ padding: 2rem; }}
                .plan-info {{ background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; padding: 1.5rem; margin: 1.5rem 0; border-radius: 1rem; text-align: center; }}
                .features {{ background: rgba(99, 102, 241, 0.1); padding: 1.5rem; margin: 1rem 0; border-radius: 0.5rem; }}
                .features ul {{ list-style: none; padding: 0; }}
                .features li {{ padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1); }}
                .cta {{ text-align: center; margin: 2rem 0; }}
                .btn {{ display: inline-block; background: #6366f1; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 0.5rem; font-weight: bold; }}
                .footer {{ background: #0f172a; padding: 1rem; text-align: center; font-size: 0.9rem; color: #94a3b8; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 style="margin: 0; color: white;">🎉 Subscription Confirmed!</h1>
                    <p style="margin: 0.5rem 0 0 0; color: rgba(255,255,255,0.9);">Welcome to {plan_name}</p>
                </div>
                
                <div class="content">
                    <h2 style="color: #10b981;">Congratulations {user_name}!</h2>
                    
                    <p>Your subscription to the <strong>{plan_name}</strong> has been successfully activated. You now have access to enhanced features and unlimited therapeutic support.</p>
                    
                    <div class="plan-info">
                        <h3 style="margin-top: 0; color: #10b981;">Your {plan_name} Features</h3>
                        <div class="features">
                            <ul>
                                {features_html}
                            </ul>
                        </div>
                    </div>
                    
                    <p>Your enhanced features are now active and ready to use. Experience the full power of AI Therapist with unlimited access to all communication modes.</p>
                    
                    <div class="cta">
                        <a href="http://localhost:8000/app" class="btn">Access Your Enhanced Features</a>
                    </div>
                    
                    <p><strong>Billing Information:</strong> Your subscription will automatically renew. You can manage your billing and subscription settings anytime from your account dashboard.</p>
                    
                    <p>Thank you for choosing AI Therapist Premium. We're committed to providing you with the best possible mental health support.</p>
                    
                    <p>Best regards,<br>The AI Therapist Team</p>
                </div>
                
                <div class="footer">
                    <p>© 2025 AI Therapist | Manage your subscription anytime</p>
                    <p>This email was sent to {user_email}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return self.send_email(user_email, subject, html_content)

# Global email service instance
email_service = EmailService()
