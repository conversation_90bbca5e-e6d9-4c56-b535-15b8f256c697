// Authentication functionality
let currentUser = null;
let authToken = null;

// Initialize authentication
function initializeAuth() {
    // Check for upgrade success
    checkUpgradeSuccess();

    // Check for stored token
    authToken = localStorage.getItem('auth_token');
    if (authToken) {
        validateToken();
    } else {
        // Redirect to login page instead of showing modal
        window.location.href = '/login';
    }
}

// Check if user just completed an upgrade
function checkUpgradeSuccess() {
    const urlParams = new URLSearchParams(window.location.search);
    const upgraded = urlParams.get('upgraded');
    const plan = urlParams.get('plan');

    if (upgraded === 'true' && plan) {
        // Show success message
        showToast(`🎉 Successfully upgraded to ${plan.charAt(0).toUpperCase() + plan.slice(1)} plan!`, 'success');

        // Clean up URL
        const cleanUrl = window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);

        // Refresh subscription info
        setTimeout(() => {
            loadUserSubscription();
        }, 1000);
    }
}

// Validate stored token
async function validateToken() {
    try {
        if (!authToken) {
            console.log('❌ No auth token found, redirecting to login');
            window.location.href = '/login';
            return;
        }

        const response = await fetch('/auth/me', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            currentUser = await response.json();
            console.log('✅ User authenticated:', currentUser.email);
            updateUIForAuthenticatedUser();
            loadUserSubscription();
        } else {
            // Token is invalid
            console.log('❌ Token invalid, redirecting to login');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('refresh_token');
            authToken = null;
            window.location.href = '/login';
        }
    } catch (error) {
        console.error('❌ Error validating token:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        authToken = null;
        window.location.href = '/login';
    }
}

// Redirect to login page instead of showing modal
function redirectToLogin() {
    window.location.href = '/login';
}

// Redirect to signup page
function redirectToSignup() {
    window.location.href = '/signup';
}

// These functions are no longer needed - using separate pages

// Handle login
async function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;
    const errorDiv = document.getElementById('login-error');
    
    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            authToken = data.access_token;
            currentUser = data.user;
            localStorage.setItem('auth_token', authToken);
            localStorage.setItem('refresh_token', data.refresh_token);
            
            hideAuthModal();
            updateUIForAuthenticatedUser();
            loadUserSubscription();
            showToast('Login successful!', 'success');
        } else {
            errorDiv.textContent = data.detail || 'Login failed';
            errorDiv.style.display = 'block';
        }
    } catch (error) {
        console.error('Login error:', error);
        errorDiv.textContent = 'Network error. Please try again.';
        errorDiv.style.display = 'block';
    }
}

// Handle registration
async function handleRegister(event) {
    event.preventDefault();
    
    const email = document.getElementById('register-email').value;
    const username = document.getElementById('register-username').value;
    const password = document.getElementById('register-password').value;
    const fullName = document.getElementById('register-fullname').value;
    const errorDiv = document.getElementById('register-error');
    
    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email,
                username,
                password,
                full_name: fullName || null
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showToast('Registration successful! Please login.', 'success');
            showLoginForm();
            // Pre-fill login email
            document.getElementById('login-email').value = email;
        } else {
            errorDiv.textContent = data.detail || 'Registration failed';
            errorDiv.style.display = 'block';
        }
    } catch (error) {
        console.error('Registration error:', error);
        errorDiv.textContent = 'Network error. Please try again.';
        errorDiv.style.display = 'block';
    }
}

// Update UI for authenticated user
function updateUIForAuthenticatedUser() {
    if (currentUser) {
        // Update user info in header
        const userInfo = document.getElementById('user-info');
        if (userInfo) {
            userInfo.innerHTML = `
                <span>Welcome, ${currentUser.full_name || currentUser.username}!</span>
                <button onclick="showUserMenu()" class="user-menu-btn">⚙️</button>
                <button onclick="logout()" class="logout-btn">Logout</button>
            `;
            userInfo.style.display = 'flex';
        }
        
        // Show subscription info
        const subscriptionInfo = document.getElementById('subscription-info');
        if (subscriptionInfo) {
            subscriptionInfo.style.display = 'block';
        }
    }
}

// Load user subscription
async function loadUserSubscription() {
    try {
        const response = await fetch('/payments/usage', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const usage = await response.json();
            updateSubscriptionUI(usage);
        }
    } catch (error) {
        console.error('Error loading subscription:', error);
    }
}

// Update subscription UI
function updateSubscriptionUI(usage) {
    const subscriptionInfo = document.getElementById('subscription-info');
    if (subscriptionInfo) {
        const minutesUsed = usage.minutes_used;
        const minutesLimit = usage.minutes_limit || 'Unlimited';
        const callsUsed = usage.phone_calls_used;
        const callsLimit = usage.phone_calls_limit || 'Unlimited';
        
        subscriptionInfo.innerHTML = `
            <div class="subscription-card">
                <h3>${usage.plan_type.charAt(0).toUpperCase() + usage.plan_type.slice(1)} Plan</h3>
                <div class="usage-stats">
                    <div class="usage-item">
                        <span>Voice/Video Minutes:</span>
                        <span>${minutesUsed} / ${minutesLimit}</span>
                    </div>
                    <div class="usage-item">
                        <span>Phone Calls:</span>
                        <span>${callsUsed} / ${callsLimit}</span>
                    </div>
                </div>
                ${usage.plan_type === 'free' ?
                    '<button onclick="showSubscriptionManagement()" class="upgrade-btn">Upgrade Plan</button>' :
                    '<button onclick="showSubscriptionManagement()" class="billing-btn">Manage Subscription</button>'
                }
            </div>
        `;
    }
}

// Upgrade and billing functions
async function showUpgradeModal() {
    try {
        // Load available plans
        const plansResponse = await fetch('/payments/plans');
        if (!plansResponse.ok) {
            throw new Error('Failed to load plans');
        }

        const plansData = await plansResponse.json();
        const plans = plansData.plans;

        // Generate plans HTML with landing page design
        const plansContainer = document.getElementById('plans-container');
        plansContainer.innerHTML = `
            <div class="pricing-grid">
                ${Object.entries(plans).map(([planType, plan]) => `
                    <div class="pricing-card ${planType === 'premium' ? 'popular' : ''}">
                        ${planType === 'premium' ? '<div class="popular-badge">Most Popular</div>' : ''}
                        <div class="plan-header">
                            <h3 class="plan-name">${plan.name}</h3>
                            <p class="plan-description">${plan.description || getDefaultDescription(planType)}</p>
                        </div>
                        <div class="plan-price">
                            <span class="currency">$</span>
                            <span class="amount">${planType === 'free' ? '0' : (plan.price_cents / 100).toFixed(0)}</span>
                            <span class="period">/month</span>
                        </div>
                        <ul class="plan-features">
                            ${plan.features.map(feature => `<li><i class="feature-icon">✓</i> ${feature}</li>`).join('')}
                        </ul>
                        <button class="plan-button ${planType === 'free' ? 'current-plan' : (planType === 'premium' ? 'premium-button' : 'basic-button')}"
                                onclick="selectPlan('${planType}')"
                                ${planType === 'free' ? 'disabled' : ''}>
                            ${planType === 'free' ? 'Current Plan' : `Choose ${plan.name}`}
                        </button>
                    </div>
                `).join('')}
            </div>
        `;

        // Helper function for default descriptions
        function getDefaultDescription(planType) {
            const descriptions = {
                'free': 'Perfect for trying out our AI therapist',
                'basic': 'Great for regular therapy sessions',
                'premium': 'Unlimited access to all features',
                'enterprise': 'Advanced features for organizations'
            };
            return descriptions[planType] || 'Professional mental health support';
        }

        // Show the modal
        document.getElementById('upgrade-modal').style.display = 'block';

    } catch (error) {
        console.error('Error loading plans:', error);
        showToast('Failed to load subscription plans. Please try again.', 'error');
    }
}

async function selectPlan(planType) {
    if (planType === 'free') return;

    try {
        // Create checkout session for selected plan
        const response = await fetch('/payments/create-checkout-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                plan_type: planType,
                success_url: `${window.location.origin}/app?upgraded=true&plan=${planType}`,
                cancel_url: window.location.href
            })
        });

        if (response.ok) {
            const data = await response.json();
            // Redirect to Stripe checkout
            window.location.href = data.checkout_url;
        } else {
            throw new Error('Failed to create checkout session');
        }
    } catch (error) {
        console.error('Error creating checkout session:', error);
        showToast('Failed to start upgrade process. Please try again.', 'error');
    }
}

async function showBillingPortal() {
    try {
        const response = await fetch('/payments/create-portal-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                return_url: window.location.href
            })
        });

        if (response.ok) {
            const data = await response.json();
            // Redirect to Stripe customer portal
            window.location.href = data.portal_url;
        } else {
            throw new Error('Failed to create portal session');
        }
    } catch (error) {
        console.error('Error creating portal session:', error);
        showToast('Failed to open billing portal. Please try again.', 'error');
    }
}

// Show subscription management section
function showSubscriptionManagement() {
    const subscriptionSection = document.getElementById('subscription-management');
    if (subscriptionSection) {
        subscriptionSection.style.display = 'block';
        loadSubscriptionManagement();

        // Scroll to the section
        subscriptionSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// Hide subscription management section
function hideSubscriptionManagement() {
    const subscriptionSection = document.getElementById('subscription-management');
    if (subscriptionSection) {
        subscriptionSection.style.display = 'none';
    }
}

// Load subscription management data
async function loadSubscriptionManagement() {
    try {
        // Load current user subscription
        const usageResponse = await fetch('/payments/usage', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (usageResponse.ok) {
            const usageData = await usageResponse.json();
            updateCurrentPlanDisplay(usageData);
        }

        // Load available plans
        const plansResponse = await fetch('/payments/plans');
        if (plansResponse.ok) {
            const plansData = await plansResponse.json();
            renderSubscriptionPlans(plansData.plans, usageData?.plan_type || 'free');
        }

    } catch (error) {
        console.error('Error loading subscription management:', error);
        showToast('Failed to load subscription information', 'error');
    }
}

// Update current plan display
function updateCurrentPlanDisplay(usageData) {
    const planNameEl = document.getElementById('current-plan-name');
    const planUsageEl = document.getElementById('current-plan-usage');

    if (planNameEl && planUsageEl) {
        const planType = usageData.plan_type || 'free';
        const planNames = {
            'free': 'Free Plan',
            'basic': 'Basic Plan',
            'premium': 'Premium Plan',
            'enterprise': 'Enterprise Plan'
        };

        planNameEl.textContent = planNames[planType] || 'Unknown Plan';

        let usageText = '';
        if (planType === 'free') {
            usageText = `${usageData.minutes_used || 0} / ${usageData.minutes_limit || 30} minutes used`;
        } else if (usageData.minutes_limit === 0) {
            usageText = 'Unlimited usage';
        } else {
            usageText = `${usageData.minutes_used || 0} / ${usageData.minutes_limit} minutes used`;
        }

        planUsageEl.textContent = usageText;
    }
}

// Render subscription plans
function renderSubscriptionPlans(plans, currentPlan) {
    const container = document.getElementById('subscription-plans-grid');
    if (!container) return;

    container.innerHTML = '';

    const planOrder = ['free', 'basic', 'premium', 'enterprise'];
    const popularPlan = 'premium';

    planOrder.forEach(planType => {
        const plan = plans[planType];
        if (plan) {
            const planCard = createSubscriptionPlanCard(planType, plan, currentPlan, planType === popularPlan);
            container.appendChild(planCard);
        }
    });
}

// Create subscription plan card
function createSubscriptionPlanCard(planType, plan, currentPlan, isPopular) {
    const card = document.createElement('div');
    card.className = `subscription-plan-card ${isPopular ? 'popular' : ''} ${planType === currentPlan ? 'current-plan' : ''}`;

    const price = planType === 'free' ? '0' : (plan.price_cents / 100).toFixed(0);

    let buttonText = 'Upgrade';
    let buttonClass = 'upgrade-btn';
    let buttonAction = `selectPlan('${planType}')`;

    if (planType === currentPlan) {
        buttonText = 'Current Plan';
        buttonClass = 'current-btn';
        buttonAction = '';
    } else if (planType === 'free') {
        buttonText = 'Downgrade';
        buttonClass = 'manage-btn';
        buttonAction = `selectPlan('${planType}')`;
    }

    card.innerHTML = `
        <h4>${plan.name}</h4>
        <div class="plan-price">
            <span class="currency">$</span>
            <span class="amount">${price}</span>
            <span class="period">/month</span>
        </div>
        <ul class="plan-features">
            ${plan.features.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
        <button class="subscription-plan-button ${buttonClass}"
                ${buttonAction ? `onclick="${buttonAction}"` : 'disabled'}>
            ${buttonText}
        </button>
    `;

    return card;
}

// Show user menu
function showUserMenu() {
    // Create a simple dropdown menu
    const existingMenu = document.getElementById('user-dropdown-menu');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }

    const menu = document.createElement('div');
    menu.id = 'user-dropdown-menu';
    menu.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--color-surface);
        border: 1px solid var(--color-border);
        border-radius: 0.5rem;
        padding: 0.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        min-width: 200px;
    `;

    menu.innerHTML = `
        <button onclick="showSubscriptionManagement(); document.getElementById('user-dropdown-menu').remove();"
                style="width: 100%; padding: 0.5rem; background: none; border: none; color: var(--color-text); text-align: left; cursor: pointer; border-radius: 0.25rem;">
            📊 Manage Subscription
        </button>
        <button onclick="showBillingPortal(); document.getElementById('user-dropdown-menu').remove();"
                style="width: 100%; padding: 0.5rem; background: none; border: none; color: var(--color-text); text-align: left; cursor: pointer; border-radius: 0.25rem;">
            💳 Billing Portal
        </button>
        <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid var(--color-border);">
        <button onclick="logout(); document.getElementById('user-dropdown-menu').remove();"
                style="width: 100%; padding: 0.5rem; background: none; border: none; color: var(--color-error); text-align: left; cursor: pointer; border-radius: 0.25rem;">
            🚪 Logout
        </button>
    `;

    // Position relative to user info
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.style.position = 'relative';
        userInfo.appendChild(menu);
    }

    // Close menu when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
            if (!menu.contains(e.target) && !e.target.classList.contains('user-menu-btn')) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        });
    }, 100);
}

// Logout
function logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    authToken = null;
    currentUser = null;

    showToast('Logged out successfully', 'info');

    // Redirect to landing page
    setTimeout(() => {
        window.location.href = '/';
    }, 1000);
}

// Initialize authentication when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize auth if we're on the main app page
    if (window.location.pathname === '/app') {
        console.log('🔐 Initializing authentication for main app');
        initializeAuth();
    }
});
