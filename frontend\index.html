<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 AI Therapist - Your 24/7 Mental Health Companion</title>
    <link rel="icon" type="image/png" href="/static/assets/Ai_Therapist.png">
    <link rel="apple-touch-icon" href="/static/assets/Ai_Therapist.png">
    <meta name="description" content="AI Therapist - Professional mental health support with voice, video, text and phone capabilities. Available 24/7 for your emotional wellbeing.">
    <meta name="keywords" content="AI therapist, mental health, therapy, counseling, emotional support, voice chat, video therapy">
    <link rel="stylesheet" href="/static/css/styles.css">
</head>

<body>
    <!-- Toast Notifications -->
    <div id="error-toast" class="toast"></div>

    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="app-logo">
                    <img src="/static/assets/Ai_Therapist.png" alt="AI Therapist" class="app-logo-img">
                    <h1>AI Therapist</h1>
                </div>
            </div>
            <div class="header-right">
                <div id="user-info" class="user-info" style="display: none;">
                    <!-- User info will be populated by JavaScript -->
                </div>
                <div id="subscription-info" class="subscription-info" style="display: none;">
                    <!-- Subscription info will be populated by JavaScript -->
                </div>
            </div>
        </div>
        <p class="subtitle">A safe space to talk about your feelings and emotions with Voice, Video, Text & Phone Support</p>
        <div class="disclaimer">
            <strong>⚠️ Important:</strong> This AI is not a substitute for professional mental health services.
            If you're experiencing a crisis, please contact emergency services or a mental health helpline immediately.
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Mode Selector -->
        <div class="mode-selector">
            <button class="mode-btn" data-mode="voice">🎤 Voice Chat</button>
            <button class="mode-btn" data-mode="video">📹 Video Chat</button>
            <button class="mode-btn active" data-mode="text">💬 Text Chat</button>
            <button class="mode-btn" data-mode="phone">📞 Phone Support</button>
        </div>

        <!-- Voice Mode -->
        <div class="voice-section">
            <div class="controls">
                <div class="input-group">
                    <label for="voice">Therapeutic Voice</label>
                    <select id="voice">
                        <option value="Aoede">Aoede (Recommended - Melodic & Soothing)</option>
                        <option value="Kore">Kore (Gentle & Empathetic)</option>
                        <option value="Charon">Charon (Calm & Reassuring)</option>
                        <option value="Puck">Puck (Warm & Conversational)</option>
                        <option value="Fenrir">Fenrir (Strong & Supportive)</option>
                    </select>
                </div>
            </div>

            <div class="wave-container">
                <div class="box-container">
                    <!-- Audio visualization bars will be added here -->
                </div>
            </div>

            <button id="start-button" class="voice-button">
                🎤 Start Voice Session
            </button>
        </div>

        <!-- Video Mode -->
        <div class="video-section">
            <div class="controls">
                <div class="input-group">
                    <label for="video-voice">Therapeutic Voice for Video</label>
                    <select id="video-voice">
                        <option value="Aoede">Aoede (Recommended - Melodic & Soothing)</option>
                        <option value="Kore">Kore (Gentle & Empathetic)</option>
                        <option value="Charon">Charon (Calm & Reassuring)</option>
                        <option value="Puck">Puck (Warm & Conversational)</option>
                        <option value="Fenrir">Fenrir (Strong & Supportive)</option>
                    </select>
                </div>
            </div>

            <div class="video-container">
                <div class="video-panel">
                    <div class="video-label">Your Video</div>
                    <video id="local-video" class="video-element" autoplay muted playsinline></video>
                    <div class="video-status" id="local-status">Camera Ready</div>
                </div>
                <div class="video-panel">
                    <div class="video-label">📁 Share Files with AI</div>
                    <div class="file-upload-panel">
                        <div class="upload-zone" id="upload-zone">
                            <div class="upload-icon">📄</div>
                            <div class="upload-text">Click or drag files here</div>
                            <div class="upload-subtext">Share documents, images, or any files with your AI Therapist</div>
                        </div>
                        <input type="file" id="file-input" class="file-input" multiple accept="*/*" title="Upload files" aria-label="Upload files">
                        <div class="uploaded-files" id="uploaded-files"></div>
                    </div>
                </div>
            </div>

            <div class="video-controls">
                <button id="start-video-button" class="video-btn">
                    📹 Start Video Session
                </button>
                <button id="toggle-camera" class="video-btn" style="display: none;">
                    📷 Toggle Camera
                </button>
                <button id="toggle-mic" class="video-btn" style="display: none;">
                    🎤 Toggle Mic
                </button>
            </div>
        </div>

        <!-- Text Mode -->
        <div class="text-section active">
            <div class="chat-container">
                <div class="messages" id="messages">
                    <div class="message ai-message">
                        <div class="avatar">🤖</div>
                        <div class="bubble">
                            <p>Hello! I'm your AI Therapist created by Critical Future. I'm here to listen and support you. How are you feeling today? You can type your thoughts, use voice, video, or even call me!</p>
                        </div>
                    </div>
                </div>

                <div class="input-area">
                    <textarea class="text-input" id="user-input" placeholder="Share your thoughts and feelings..." rows="1"></textarea>
                    <div class="input-buttons">
                        <button class="input-btn" id="mic-btn" title="Speak">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                <line x1="12" y1="19" x2="12" y2="23"></line>
                                <line x1="8" y1="23" x2="16" y2="23"></line>
                            </svg>
                        </button>
                        <button class="input-btn" id="send-btn" title="Send">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2 21l21-9L2 3v7l15 2-15 2v7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Phone Mode -->
        <div class="phone-section">
            <div class="phone-info">
                <h2 class="phone-title">📞 Call AI Therapist Directly</h2>
                <div class="phone-number" id="phone-number">******-713-4471</div>
                <div class="access-code">
                    Access Code: <span id="access-code">163760</span>
                </div>
                <div class="phone-description">
                    <p><strong>🌟 24/7 Instant Support</strong></p>
                    <p>Call this number anytime you need emotional support. When prompted, enter the access code above. Our AI Therapist is available 24/7 to provide compassionate care through voice conversations.</p>
                    <p><strong>How it works:</strong></p>
                    <ol>
                        <li>📞 Dial the phone number above</li>
                        <li>🔢 Enter the access code when prompted</li>
                        <li>🗣️ Start talking with your AI therapist</li>
                        <li>💙 Receive compassionate, professional support</li>
                    </ol>
                </div>

                <div class="phone-actions">
                    <button class="phone-cta-btn" onclick="copyPhoneInfo()">
                        📋 Copy Phone Info
                    </button>
                    <button class="phone-refresh-btn" onclick="loadPhoneData()">
                        🔄 Refresh Code
                    </button>
                </div>

                <div class="phone-stats">
                    <div class="phone-stat">
                        <div class="phone-stat-value" id="total-calls">0</div>
                        <div class="phone-stat-label">Total Calls</div>
                    </div>
                    <div class="phone-stat">
                        <div class="phone-stat-value success" id="active-calls">0</div>
                        <div class="phone-stat-label">Active Calls</div>
                    </div>
                    <div class="phone-stat">
                        <div class="phone-stat-value" id="phone-status">🟢</div>
                        <div class="phone-stat-label">Service Status</div>
                    </div>
                </div>
            </div>

            <div class="call-logs">
                <h3>📞 Recent Call Logs</h3>
                <div id="call-logs-container">
                    <div class="no-calls">
                        📞 No calls yet. Share your phone number with people who need support!
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Status Indicator -->
    <div class="status-indicator" id="status">
        <span id="status-text">Ready</span>
    </div>

    <!-- Audio Element for Playback -->
    <audio id="audio-output" autoplay></audio>

    <!-- JavaScript Files -->
    <script src="/static/js/app.js"></script>
    <script src="/static/js/file-upload.js"></script>
    <script src="/static/js/text-chat.js"></script>
    <script src="/static/js/phone.js"></script>
    <script src="/static/js/voice.js"></script>
    <script src="/static/js/video.js"></script>
    <script src="/static/js/audio-visualization.js"></script>
    <script src="/static/js/auth.js"></script>

    <script>
        // Initialize text chat microphone after DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setupTextChatMicrophone();
        });
    </script>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="modal">
        <div class="modal-content auth-modal">
            <h2 id="auth-title">Login to AI Therapist</h2>

            <!-- Login Form -->
            <form id="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="login-email">Email:</label>
                    <input type="email" id="login-email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password:</label>
                    <input type="password" id="login-password" required>
                </div>
                <div id="login-error" class="error-message"></div>
                <button type="submit" class="auth-btn">Login</button>
                <p class="auth-switch">
                    Don't have an account?
                    <a href="#" onclick="showRegisterForm()">Sign up</a>
                </p>
            </form>

            <!-- Register Form -->
            <form id="register-form" onsubmit="handleRegister(event)" style="display: none;">
                <div class="form-group">
                    <label for="register-email">Email:</label>
                    <input type="email" id="register-email" required>
                </div>
                <div class="form-group">
                    <label for="register-username">Username:</label>
                    <input type="text" id="register-username" required>
                </div>
                <div class="form-group">
                    <label for="register-fullname">Full Name (optional):</label>
                    <input type="text" id="register-fullname">
                </div>
                <div class="form-group">
                    <label for="register-password">Password:</label>
                    <input type="password" id="register-password" required>
                </div>
                <div id="register-error" class="error-message"></div>
                <button type="submit" class="auth-btn">Create Account</button>
                <p class="auth-switch">
                    Already have an account?
                    <a href="#" onclick="showLoginForm()">Login</a>
                </p>
            </form>
        </div>
    </div>

    <!-- Subscription Management Section -->
    <div id="subscription-management" class="subscription-section" style="display: none;">
        <div class="subscription-container">
            <div class="subscription-header">
                <h2>🎯 Manage Your Subscription</h2>
                <p>Choose the perfect plan for your mental health journey</p>
            </div>

            <div class="current-plan-info">
                <div class="current-plan-card">
                    <h3>Current Plan</h3>
                    <div id="current-plan-details">
                        <div class="plan-name" id="current-plan-name">Loading...</div>
                        <div class="plan-usage" id="current-plan-usage">Loading...</div>
                    </div>
                </div>
            </div>

            <div class="available-plans">
                <h3>Available Plans</h3>
                <div id="subscription-plans-grid" class="subscription-plans-grid">
                    <!-- Plans will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Upgrade Modal -->
    <div id="upgrade-modal" class="modal">
        <div class="modal-content upgrade-modal">
            <div class="modal-header">
                <h2>Choose Your Plan</h2>
                <span class="close" onclick="document.getElementById('upgrade-modal').style.display='none'">&times;</span>
            </div>
            <div id="plans-container" class="plans-container">
                <!-- Plans will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <img src="/static/assets/Ai_Therapist.png" alt="AI Therapist" class="footer-logo-img">
                <span class="footer-brand">AI Therapist</span>
            </div>
            <p class="footer-description">Your trusted AI companion for mental health support</p>
            <div class="footer-links">
                <a href="/privacy" class="footer-link">Privacy Policy</a>
                <a href="/terms" class="footer-link">Terms of Service</a>
                <a href="/contact" class="footer-link">Contact</a>
            </div>
            <p class="footer-copyright">
                © 2025 AI Therapist. Powered by <strong>Critical Future</strong>
            </p>
        </div>
    </footer>
</body>

</html>
