#!/usr/bin/env python3
"""
Phone Mode Runner for AI Therapist
Run this script to start the AI Therapist in phone mode
"""
import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Start AI Therapist in phone mode"""
    logger.info("🔧 Starting AI Therapist in PHONE mode...")
    
    # Set environment variable for phone mode
    os.environ["MODE"] = "PHONE"
    
    # Import and run the main application
    try:
        from backend.app.main import app
        import uvicorn
        
        logger.info("📞 Phone mode activated!")
        logger.info("📋 Instructions:")
        logger.info("   1. Wait for the phone number and access code to appear")
        logger.info("   2. Call the displayed phone number")
        logger.info("   3. Enter the access code when prompted")
        logger.info("   4. Talk with the AI therapist!")
        logger.info("   5. Press Ctrl+C to stop the phone service")
        logger.info("")
        
        # This will start the phone service
        exec(open("backend/app/main.py").read())
        
    except KeyboardInterrupt:
        logger.info("📞 Phone service stopped by user")
    except Exception as e:
        logger.error(f"❌ Error starting phone mode: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
