"""
Phone call handler for AI Therapist
"""
import asyncio
import time
import logging
import numpy as np
from typing import Literal

from backend.config.settings import get_settings, SYSTEM_PROMPT
from backend.services.webrtc_service import AsyncStream<PERSON>andler, FASTRTC_AVAILABLE
from backend.services.gemini_service import GeminiService
from backend.utils.audio_utils import detect_goodbye
from backend.utils.session_manager import add_session, remove_session, add_call_log, update_call_log

logger = logging.getLogger(__name__)

class PhoneHandler(AsyncStreamHandler):
    """Handler for phone call interactions with AI Therapist"""
    
    def __init__(
        self,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 8000,  # Phone quality
    ) -> None:
        super().__init__(
            expected_layout,
            output_sample_rate,
            input_sample_rate=8000,  # Phone quality
        )
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()
        self.session = None
        self.session_id = None
        self.call_id = None
        self.caller_number = None
        self.call_start_time = None
        self.settings = get_settings()
        self.phone_mode = True  # Always in phone mode
        
    def copy(self) -> "PhoneHandler":
        return PhoneHandler(
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
        )

    async def start_up(self):
        """Initialize phone call session"""
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping phone handler setup")
            return
            
        try:
            self.call_start_time = time.time()
            
            # Extract caller information from args if available
            try:
                await self.wait_for_args()
                if self.latest_args and len(self.latest_args) > 0:
                    self.caller_number = self.latest_args[0]
            except Exception as args_error:
                logger.warning(f"Could not get caller info: {args_error}")
                self.caller_number = "Unknown"

            # Log the call
            self.call_id = add_call_log(self.caller_number, "answered").id
            
            # Initialize Gemini service
            gemini_service = GeminiService()
            self.client = gemini_service.get_client()

            # Create phone-optimized configuration
            try:
                from google.genai.types import (
                    LiveConnectConfig,
                    PrebuiltVoiceConfig,
                    SpeechConfig,
                    VoiceConfig,
                )
                
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name="Charon",  # Calm voice for phone calls
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.7,  # Slightly more conservative for phone
                        "max_output_tokens": 200,  # Shorter responses for phone
                    },
                    system_instruction={
                        "parts": [{"text": f"{SYSTEM_PROMPT}\n\nIMPORTANT: You are speaking on a phone call. Keep responses concise and clear. The caller cannot see you, so focus on voice-only communication."}]
                    }
                )
                logger.info("Phone LiveConnectConfig created with Charon voice")
            except Exception as config_error:
                logger.warning(f"Failed to create phone config: {config_error}")
                return
            
            logger.info(f"Starting phone session for caller: {self.caller_number}")
            
            # Start Gemini Live session for phone
            async with self.client.aio.live.connect(
                model="gemini-2.0-flash-exp", 
                config=config
            ) as session:
                logger.info("Phone Gemini Live session established")
                self.session = session
                self.session_id = f"phone_{int(time.time())}"
                add_session(self.session_id, "phone", self)
                
                # Send phone-specific setup
                try:
                    phone_intro = (
                        f"You are now on a phone call with someone who needs therapeutic support. "
                        f"This is a voice-only conversation. Be warm, empathetic, and provide "
                        f"excellent therapeutic care. Keep responses clear and concise for phone quality."
                    )
                    await session.send({"text": phone_intro})
                    logger.info("Phone session setup sent")
                except Exception as prompt_error:
                    logger.warning(f"Could not send phone setup: {prompt_error}")
                
                # Process phone call stream
                async for chunk in session.start_stream(
                    stream=self.stream(), 
                    mime_type="audio/pcm"
                ):
                    if self.quit.is_set():
                        logger.info("Phone call ended by quit signal")
                        break
                        
                    if chunk.data:
                        try:
                            array = np.frombuffer(chunk.data, dtype=np.int16)
                            if not self.quit.is_set() and array.size > 0:
                                try:
                                    self.output_queue.put_nowait((self.output_sample_rate, array))
                                except asyncio.QueueFull:
                                    logger.warning("Phone output queue full, dropping audio")
                        except Exception as audio_error:
                            logger.error(f"Error processing phone audio: {audio_error}")
                    
                    if chunk.text:
                        logger.info(f"Phone AI response: {chunk.text[:100]}...")
                        
                        # Check for goodbye and auto-end call
                        if detect_goodbye(chunk.text):
                            logger.info("Goodbye detected, ending phone call")
                            await asyncio.sleep(3)  # Let the goodbye message play
                            self.quit.set()
                            break

        except Exception as e:
            logger.error(f"Error in phone call handler: {e}")
        finally:
            # Clean up phone call
            if self.call_id and self.call_start_time:
                duration = int(time.time() - self.call_start_time)
                update_call_log(self.call_id, "ended", duration)
                logger.info(f"Phone call ended, duration: {duration} seconds")
            
            if self.session_id:
                remove_session(self.session_id)

    async def stream(self):
        """Stream audio data to Gemini for phone calls"""
        while not self.quit.is_set():
            try:
                audio_data = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio_data
            except (asyncio.TimeoutError, TimeoutError):
                pass
            except Exception as e:
                logger.error(f"Error in phone audio streaming: {e}")
                break

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from phone caller"""
        try:
            if self.quit.is_set():
                return
                
            _, array = frame
            array = array.squeeze()
            
            if array.size == 0:
                return
            
            # Convert to phone quality (8kHz, mono)
            audio_bytes = array.astype(np.int16).tobytes()
            
            if not self.quit.is_set():
                try:
                    self.input_queue.put_nowait(audio_bytes)
                except asyncio.QueueFull:
                    logger.warning("Phone input queue full, dropping audio")
                    
        except Exception as e:
            logger.error(f"Error processing phone input audio: {e}")

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio response to phone caller"""
        try:
            if self.quit.is_set():
                return None
            return await asyncio.wait_for(self.output_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error emitting phone audio: {e}")
            return None

    def shutdown(self) -> None:
        """Clean shutdown of phone call"""
        logger.info("Shutting down phone call handler")
        self.quit.set()
        
        # Update call log
        if self.call_id and self.call_start_time:
            duration = int(time.time() - self.call_start_time)
            update_call_log(self.call_id, "ended", duration)
        
        if self.session_id:
            remove_session(self.session_id)
        
        # Clear queues
        while not self.input_queue.empty():
            try:
                self.input_queue.get_nowait()
            except:
                break
                
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except:
                break
