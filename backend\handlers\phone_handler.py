"""
Phone call handler for AI Therapist
"""
import asyncio
import time
import logging
import numpy as np
from typing import Literal

from backend.config.settings import get_settings, SYSTEM_PROMPT
from backend.services.webrtc_service import AsyncStream<PERSON>and<PERSON>, FASTRTC_AVAILABLE
from backend.services.gemini_service import GeminiService
from backend.utils.audio_utils import detect_goodbye
from backend.utils.session_manager import add_session, remove_session, add_call_log, update_call_log

logger = logging.getLogger(__name__)

class PhoneHandler(AsyncStreamHandler):
    """Handler for phone call interactions with AI Therapist"""

    def __init__(
        self,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 16000,  # Improved phone quality
    ) -> None:
        super().__init__(
            expected_layout,
            output_sample_rate,
            input_sample_rate=16000,  # Improved phone quality
        )
        self.input_queue: asyncio.Queue = asyncio.Queue(maxsize=500)
        self.output_queue: asyncio.Queue = asyncio.Queue(maxsize=500)
        self.quit: asyncio.Event = asyncio.Event()
        self.session = None
        self.session_id = None
        self.call_id = None
        self.caller_number = None
        self.call_start_time = None
        self.settings = get_settings()
        self.phone_mode = True  # Always in phone mode
        self.client = None
        self.latest_args = None

        # Audio quality improvements
        self.noise_gate_threshold = 0.01  # Improved noise gate
        self.audio_smoothing_factor = 0.2  # Better audio smoothing
        self.last_audio_level = 0.0
        self.audio_buffer = []
        self.min_audio_length = 160  # Minimum audio chunk size for quality
        self.conversation_history = []
        self.gemini_service = GeminiService()
        
    def copy(self) -> "PhoneHandler":
        return PhoneHandler(
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
        )

    async def start_up(self):
        """Initialize phone call session"""
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping phone handler setup")
            return
            
        try:
            self.call_start_time = time.time()
            
            # Extract caller information from args if available
            try:
                await asyncio.wait_for(self.wait_for_args(), timeout=3.0)
                if self.latest_args and len(self.latest_args) > 0:
                    self.caller_number = self.latest_args[0]
            except (asyncio.TimeoutError, Exception) as args_error:
                logger.warning(f"Could not get caller info: {args_error}")
                self.caller_number = "Unknown Caller"

            # Log the call with proper phone number
            if self.caller_number:
                self.call_id = add_call_log(self.caller_number, "answered").id
            else:
                self.call_id = add_call_log("Unknown Caller", "answered").id
            
            # Initialize Gemini service with proper event loop handling
            try:
                gemini_service = GeminiService()
                self.client = gemini_service.get_client()
            except Exception as e:
                logger.error(f"Error initializing Gemini service: {e}")
                return

            # Create phone-optimized configuration
            try:
                from google.genai.types import (
                    LiveConnectConfig,
                    PrebuiltVoiceConfig,
                    SpeechConfig,
                    VoiceConfig,
                )
                
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name="Charon",  # Calm voice for phone calls
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.7,  # Slightly more conservative for phone
                        "max_output_tokens": 200,  # Shorter responses for phone
                    },
                    system_instruction={
                        "parts": [{"text": f"{SYSTEM_PROMPT}\n\nIMPORTANT: You are speaking on a phone call. Keep responses concise and clear. The caller cannot see you, so focus on voice-only communication."}]
                    }
                )
                logger.info("Phone LiveConnectConfig created with Charon voice")
            except Exception as config_error:
                logger.warning(f"Failed to create phone config: {config_error}")
                return
            
            logger.info(f"Starting phone session for caller: {self.caller_number}")
            
            # Start Gemini Live session for phone
            async with self.client.aio.live.connect(
                model="gemini-2.0-flash-exp", 
                config=config
            ) as session:
                logger.info("Phone Gemini Live session established")
                self.session = session
                self.session_id = f"phone_{int(time.time())}"
                add_session(self.session_id, "phone", self)
                
                # Send phone-specific setup and greeting
                try:
                    phone_intro = (
                        f"You are now on a phone call with someone who needs therapeutic support. "
                        f"This is a voice-only conversation. Be warm, empathetic, and provide "
                        f"excellent therapeutic care. Keep responses clear and concise for phone quality. "
                        f"Always respond with audio/voice, never just text. Start by greeting the caller warmly."
                    )
                    await session.send({"text": phone_intro})
                    logger.info("Phone session setup sent")

                    # Send initial greeting that will trigger voice response
                    greeting = (
                        "Please greet the caller warmly and ask how you can help them today. "
                        "Say: Hello! Welcome to AI Therapist. I'm here to provide you with compassionate "
                        "support and guidance. How are you feeling today, and what would you like to talk about?"
                    )
                    await session.send({"text": greeting})
                    logger.info("Phone greeting sent - should trigger voice response")

                except Exception as prompt_error:
                    logger.warning(f"Could not send phone setup: {prompt_error}")
                
                # Process phone call stream
                async for chunk in session.start_stream(
                    stream=self.stream(),
                    mime_type="audio/pcm"
                ):
                    if self.quit.is_set():
                        logger.info("Phone call ended by quit signal")
                        break

                    if chunk.data and len(chunk.data) > 0:
                        try:
                            # Convert audio data to proper phone format
                            array = np.frombuffer(chunk.data, dtype=np.int16)
                            if not self.quit.is_set() and array is not None and array.size > 0:
                                # Ensure mono audio for phone
                                if array.ndim > 1:
                                    array = array.mean(axis=1).astype(np.int16)

                                # Apply audio quality improvements
                                if len(array) > 0:
                                    # Normalize audio to prevent distortion
                                    if np.max(np.abs(array)) > 0:
                                        array = (array.astype(np.float32) / np.max(np.abs(array)) * 0.8 * 32767).astype(np.int16)

                                    try:
                                        # Use improved sample rate
                                        self.output_queue.put_nowait((16000, array))
                                        logger.info(f"Phone audio queued: {len(array)} samples at 16kHz - AI speaking")
                                    except asyncio.QueueFull:
                                        # Drop oldest audio if queue is full
                                        try:
                                            self.output_queue.get_nowait()
                                            self.output_queue.put_nowait((16000, array))
                                            logger.warning("Phone output queue was full, dropped old audio")
                                        except asyncio.QueueEmpty:
                                            pass
                        except Exception as audio_error:
                            logger.error(f"Error processing phone audio: {audio_error}")

                    if chunk.text:
                        logger.info(f"Phone AI response: {chunk.text[:100]}...")

                        # Check for goodbye and auto-end call
                        if detect_goodbye(chunk.text):
                            logger.info("Goodbye detected, ending phone call")
                            await asyncio.sleep(3)  # Let the goodbye message play
                            self.quit.set()
                            break

        except Exception as e:
            logger.error(f"Error in phone call handler: {e}")
        finally:
            # Clean up phone call
            if self.call_id and self.call_start_time:
                duration = int(time.time() - self.call_start_time)
                update_call_log(self.call_id, "ended", duration)
                logger.info(f"Phone call ended, duration: {duration} seconds")
            
            if self.session_id:
                remove_session(self.session_id)

    async def stream(self):
        """Stream audio data to Gemini for phone calls"""
        while not self.quit.is_set():
            try:
                audio_data = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio_data
            except (asyncio.TimeoutError, TimeoutError):
                pass
            except Exception as e:
                logger.error(f"Error in phone audio streaming: {e}")
                break

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from phone caller"""
        try:
            if self.quit.is_set():
                return

            if frame is None or len(frame) != 2:
                return

            sample_rate, array = frame
            if array is None:
                return

            array = array.squeeze()

            if array is None or array.size == 0:
                return

            # Ensure mono audio for phone calls
            if array.ndim > 1:
                array = array.mean(axis=1)

            # Apply noise gate to reduce background noise
            audio_level = np.abs(array).mean()
            if audio_level < self.noise_gate_threshold:
                return  # Skip very quiet audio to reduce noise

            # Normalize audio to prevent clipping
            if np.max(np.abs(array)) > 0:
                array = array / np.max(np.abs(array)) * 0.8

            # Convert to improved phone quality (16kHz, mono, int16)
            array = (array * 32767).astype(np.int16)
            audio_bytes = array.tobytes()

            if not self.quit.is_set() and len(audio_bytes) > 0:
                try:
                    # If queue is full, remove oldest item to make space
                    if self.input_queue.full():
                        try:
                            self.input_queue.get_nowait()
                        except asyncio.QueueEmpty:
                            pass

                    self.input_queue.put_nowait(audio_bytes)
                    logger.debug(f"Phone input queued: {len(audio_bytes)} bytes")
                except asyncio.QueueFull:
                    # This should rarely happen now
                    pass

        except Exception as e:
            logger.error(f"Error processing phone input audio: {e}")

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio response to phone caller"""
        try:
            if self.quit.is_set():
                return None
            return await asyncio.wait_for(self.output_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error emitting phone audio: {e}")
            return None

    def shutdown(self) -> None:
        """Clean shutdown of phone call"""
        logger.info("Shutting down phone call handler")
        self.quit.set()
        
        # Update call log
        if self.call_id and self.call_start_time:
            duration = int(time.time() - self.call_start_time)
            update_call_log(self.call_id, "ended", duration)
        
        if self.session_id:
            remove_session(self.session_id)
        
        # Clear queues
        while not self.input_queue.empty():
            try:
                self.input_queue.get_nowait()
            except:
                break
                
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except:
                break
