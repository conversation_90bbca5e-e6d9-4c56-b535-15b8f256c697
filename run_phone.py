#!/usr/bin/env python3
"""
Phone mode runner for AI Therapist
This script runs the application in phone mode and captures FastPhone credentials
"""
import os
import sys
import logging
import asyncio
import subprocess
import re
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_phone_credentials(output_line: str):
    """Extract phone credentials from FastPhone output"""
    patterns = [
        r'Phone:\s*(\+?\d[\d\s\-\(\)]+).*?Access\s*[Cc]ode:\s*(\d+)',
        r'(\+\d[\d\s\-]+).*?(\d{6})',
        r'Call\s+(\+\d[\d\s\-]+).*?code\s+(\d+)',
        r'Number:\s*(\+\d[\d\s\-]+).*?Code:\s*(\d+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, output_line, re.IGNORECASE)
        if match:
            phone = match.group(1).strip()
            code = match.group(2).strip()
            return phone, code
    
    return None, None

async def update_credentials_via_api(phone_number: str, access_code: str):
    """Update credentials via API call"""
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/phone/update-credentials",
                params={
                    "phone_number": phone_number,
                    "access_code": access_code
                }
            )
            if response.status_code == 200:
                logger.info(f"Successfully updated credentials via API: {phone_number} (Code: {access_code})")
                return True
            else:
                logger.error(f"Failed to update credentials via API: {response.status_code}")
                return False
    except Exception as e:
        logger.error(f"Error updating credentials via API: {e}")
        return False

def run_phone_mode():
    """Run the application in phone mode"""
    logger.info("Starting AI Therapist in Phone Mode")

    # Set environment variable for phone mode
    os.environ["MODE"] = "PHONE"

    try:
        # Start the application with environment variable
        env = os.environ.copy()
        env["MODE"] = "PHONE"

        process = subprocess.Popen(
            [sys.executable, "run.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            env=env
        )
        
        logger.info("Application started, monitoring for phone credentials...")
        
        # Monitor output for phone credentials
        credentials_found = False
        for line in iter(process.stdout.readline, ''):
            if line:
                print(line.strip())  # Print all output
                
                # Look for phone credentials
                if not credentials_found:
                    phone, code = extract_phone_credentials(line)
                    if phone and code:
                        logger.info(f"🎉 Found phone credentials: {phone} (Access Code: {code})")
                        credentials_found = True
                        
                        # Try to update via API (in background)
                        asyncio.create_task(update_credentials_via_api(phone, code))
                        
                        print(f"\n{'='*60}")
                        print(f"📞 PHONE SERVICE READY!")
                        print(f"📞 Phone Number: {phone}")
                        print(f"🔑 Access Code: {code}")
                        print(f"{'='*60}\n")
        
        # Wait for process to complete
        process.wait()
        
    except KeyboardInterrupt:
        logger.info("Shutting down phone service...")
        if 'process' in locals():
            process.terminate()
            process.wait()
    except Exception as e:
        logger.error(f"Error running phone mode: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = run_phone_mode()
    sys.exit(exit_code)
