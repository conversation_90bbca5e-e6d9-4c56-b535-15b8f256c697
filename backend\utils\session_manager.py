"""
Session management utilities for AI Therapist
"""
import time
from typing import Dict, Any, List
from datetime import datetime
from backend.models.schemas import CallLogEntry

# Global session storage
active_sessions: Dict[str, Dict[str, Any]] = {}
call_logs: List[CallLogEntry] = []

# Phone configuration
PHONE_NUMBER = "******-THERAPY"  # This would be your actual phone number

def add_session(session_id: str, session_type: str, handler: Any) -> None:
    """Add a new active session"""
    active_sessions[session_id] = {
        "type": session_type,
        "handler": handler,
        "created_at": datetime.utcnow().isoformat()
    }

def remove_session(session_id: str) -> bool:
    """Remove an active session"""
    if session_id in active_sessions:
        del active_sessions[session_id]
        return True
    return False

def get_session(session_id: str) -> Dict[str, Any]:
    """Get session information"""
    return active_sessions.get(session_id)

def get_all_sessions() -> Dict[str, Dict[str, Any]]:
    """Get all active sessions"""
    return active_sessions.copy()

def cleanup_sessions() -> None:
    """Clean up all active sessions"""
    for session_id, session_info in list(active_sessions.items()):
        handler = session_info.get("handler")
        if handler and hasattr(handler, "shutdown"):
            handler.shutdown()
    active_sessions.clear()

def add_call_log(phone_number: str, status: str, session_id: str = None) -> CallLogEntry:
    """Add a new call log entry"""
    call_entry = CallLogEntry(
        id=f"call_{int(time.time())}_{len(call_logs)}",
        phone_number=phone_number,
        timestamp=datetime.utcnow().isoformat(),
        status=status,
        session_id=session_id
    )
    call_logs.append(call_entry)
    return call_entry

def update_call_log(call_id: str, status: str, duration: int = None) -> bool:
    """Update an existing call log entry"""
    for call in call_logs:
        if call.id == call_id:
            call.status = status
            if duration is not None:
                call.duration = duration
            return True
    return False

def get_call_logs() -> List[CallLogEntry]:
    """Get all call logs"""
    return call_logs.copy()

def get_phone_number() -> str:
    """Get the configured phone number"""
    return PHONE_NUMBER
