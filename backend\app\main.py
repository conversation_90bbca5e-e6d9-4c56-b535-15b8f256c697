"""
Main FastAPI application for AI Therapist
"""
import logging
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles

from backend.config.settings import get_settings, current_dir
from backend.routes import chat, webrtc, phone, system, auth, payments
from backend.services.webrtc_service import (
    Stream, 
    get_cloudflare_turn_credentials_async,
    is_webrtc_available
)
from backend.handlers.gemini_handler import Gemini<PERSON><PERSON><PERSON>
from backend.handlers.video_handler import Gemini<PERSON>ideoH<PERSON>ler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Initialize FastAPI app
app = FastAPI(
    title="AI Therapist with Gemini Voice & Video",
    description="A therapeutic AI assistant with real-time voice and video capabilities powered by Google Gemini",
    version="2.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files (check both old and new locations)
static_dir = current_dir / "static"
frontend_static_dir = current_dir / "frontend"

if frontend_static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_static_dir)), name="static")
    logger.info("Frontend static files mounted")
elif static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info("Legacy static files mounted")
else:
    logger.warning("No static directory found, skipping static file mounting")

# Include routers
app.include_router(auth.router, tags=["auth"])
app.include_router(payments.router, tags=["payments"])
app.include_router(chat.router, tags=["chat"])
app.include_router(webrtc.router, tags=["webrtc"])
app.include_router(phone.router, tags=["phone"])
app.include_router(system.router, tags=["system"])

# Initialize WebRTC streams if available
if is_webrtc_available():
    try:
        # Audio-only stream
        audio_stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=GeminiHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=5,
            time_limit=300,
        )
        
        # Video stream
        video_stream = Stream(
            modality="audio-video", 
            mode="send-receive",
            handler=GeminiVideoHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=3,
            time_limit=300,
        )
        
        # Mount streams
        audio_stream.mount(app, path="/audio")
        video_stream.mount(app, path="/video")
        logger.info("FastRTC streams mounted successfully")
        
        # Add stream input setting functionality
        @app.post("/input_hook")
        async def set_input_hook(body: dict):
            """Enhanced input hook that actually sets stream inputs"""
            try:
                webrtc_id = body.get("webrtc_id")
                voice_name = body.get("voice_name", "Aoede")
                mode = body.get("mode", "audio")
                uploaded_files = body.get("uploaded_files")
                
                # Validate voice name
                valid_voices = ["Puck", "Charon", "Kore", "Fenrir", "Aoede"]
                if voice_name not in valid_voices:
                    voice_name = "Aoede"
                
                # Set input for appropriate stream
                if mode == "video":
                    video_stream.set_input(webrtc_id, voice_name, uploaded_files)
                else:
                    audio_stream.set_input(webrtc_id, voice_name)
                    
                logger.info(f"Input set for WebRTC ID: {webrtc_id}, Voice: {voice_name}, Mode: {mode}")
                return {"status": "ok", "webrtc_id": webrtc_id, "voice": voice_name, "mode": mode}
                
            except Exception as e:
                logger.error(f"Error setting input: {e}")
                return {"status": "error", "message": str(e)}
        
    except Exception as e:
        logger.error(f"Error initializing FastRTC Streams: {e}")
else:
    logger.warning("FastRTC not available, WebRTC features disabled")

@app.get("/", response_class=HTMLResponse)
async def get_main_page():
    """Redirect to landing page"""
    try:
        landing_path = current_dir / "frontend" / "landing.html"
        if landing_path.exists():
            html_content = landing_path.read_text(encoding='utf-8')
            return HTMLResponse(content=html_content)

        # Fallback to main app if landing page doesn't exist
        html_file = current_dir / "frontend" / "index.html"
        if not html_file.exists():
            # Fall back to old location
            html_file = current_dir / "index.html"
        
        if html_file.exists():
            try:
                html_content = html_file.read_text(encoding='utf-8')
                
                # Get RTC configuration
                try:
                    rtc_config = await get_cloudflare_turn_credentials_async()
                    rtc_config_str = str(rtc_config).replace("'", '"')
                except Exception as rtc_error:
                    logger.warning(f"Could not get RTC config: {rtc_error}")
                    rtc_config_str = '{"iceServers": []}'
                
                # Inject RTC configuration into HTML
                if "RTC_CONFIGURATION = {" in html_content:
                    # Replace the existing RTC configuration
                    start_marker = "RTC_CONFIGURATION = {"
                    end_marker = "};"
                    start_idx = html_content.find(start_marker)
                    if start_idx != -1:
                        end_idx = html_content.find(end_marker, start_idx) + len(end_marker)
                        if end_idx > start_idx:
                            html_content = (
                                html_content[:start_idx] + 
                                f"RTC_CONFIGURATION = {rtc_config_str};" +
                                html_content[end_idx:]
                            )
                
                return HTMLResponse(content=html_content)
                
            except Exception as read_error:
                logger.error(f"Error reading HTML file: {read_error}")
                return HTMLResponse(content=f"""
<!DOCTYPE html>
<html>
<head><title>AI Therapist - File Error</title></head>
<body>
    <h1>File Read Error</h1>
    <p>Could not read HTML file: {read_error}</p>
    <p><a href="/debug">Debug Info</a></p>
</body>
</html>
                """, status_code=503)
        else:
            return HTMLResponse(content=f"""
<!DOCTYPE html>
<html>
<head><title>AI Therapist - File Not Found</title></head>
<body>
    <h1>HTML File Not Found</h1>
    <p>Could not find index.html in expected locations</p>
    <p>Checked: {current_dir / 'frontend' / 'index.html'} and {current_dir / 'index.html'}</p>
    <p><a href="/debug">Debug Info</a></p>
</body>
</html>
            """, status_code=404)
            
    except Exception as e:
        logger.error(f"Error serving main page: {e}")
        return HTMLResponse(content=f"""
<!DOCTYPE html>
<html>
<head><title>AI Therapist - Server Error</title></head>
<body>
    <h1>Server Error</h1>
    <p>An error occurred: {e}</p>
    <p><a href="/debug">Debug Info</a></p>
</body>
</html>
        """, status_code=500)

@app.get("/landing", response_class=HTMLResponse)
async def serve_landing_page():
    """Serve the landing page"""
    try:
        landing_path = current_dir / "frontend" / "landing.html"
        if landing_path.exists():
            html_content = landing_path.read_text(encoding='utf-8')
            return HTMLResponse(content=html_content)
        else:
            return HTMLResponse(content="<h1>Landing page not found</h1>", status_code=404)
    except Exception as e:
        logger.error(f"Error serving landing page: {e}")
        return HTMLResponse(content=f"<h1>Error: {e}</h1>", status_code=500)

@app.get("/app", response_class=HTMLResponse)
async def serve_main_app():
    """Serve the main application page (redirect from main route)"""
    return await get_main_page()

@app.get("/login", response_class=HTMLResponse)
async def serve_login_page():
    """Serve the login page"""
    try:
        login_path = current_dir / "frontend" / "login.html"
        if login_path.exists():
            html_content = login_path.read_text(encoding='utf-8')
            return HTMLResponse(content=html_content)
        else:
            return HTMLResponse(content="<h1>Login page not found</h1>", status_code=404)
    except Exception as e:
        logger.error(f"Error serving login page: {e}")
        return HTMLResponse(content=f"<h1>Error: {e}</h1>", status_code=500)

@app.get("/signup", response_class=HTMLResponse)
async def serve_signup_page():
    """Serve the signup page"""
    try:
        signup_path = current_dir / "frontend" / "signup.html"
        if signup_path.exists():
            html_content = signup_path.read_text(encoding='utf-8')
            return HTMLResponse(content=html_content)
        else:
            return HTMLResponse(content="<h1>Signup page not found</h1>", status_code=404)
    except Exception as e:
        logger.error(f"Error serving signup page: {e}")
        return HTMLResponse(content=f"<h1>Error: {e}</h1>", status_code=500)

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("AI Therapist API starting up...")

    # Initialize database
    try:
        from backend.models.database import init_database
        init_database()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")

    # Initialize Gemini service
    from backend.services.gemini_service import GeminiService
    gemini_service = GeminiService()
    logger.info("Gemini service initialized")

    # Test WebRTC availability
    if is_webrtc_available():
        logger.info("WebRTC services are available")
    else:
        logger.warning("WebRTC services are not available")

if __name__ == "__main__":
    import os
    import uvicorn

    mode = os.getenv("MODE") or settings.mode
    logger.info(f"🔧 Detected mode: {mode} (from env: {os.getenv('MODE')}, from settings: {settings.mode})")

    if mode == "PHONE":
        logger.info("📞 Starting in PHONE mode")

        # Start phone credentials monitoring
        from backend.services.phone_monitor import start_phone_monitoring
        start_phone_monitoring()

        # Start FastPhone service
        logger.info("Starting FastPhone service...")
        audio_stream.fastphone(host="0.0.0.0", port=settings.port)
    else:
        logger.info("🌐 Starting in WEB mode")
        uvicorn.run(app, host="0.0.0.0", port=settings.port)
