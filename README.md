﻿# 🧠 Real-time-AI-Therapist - Your Mental Health Companion

<div align="center">
  <img src="assets/Ai_Therapist.png" alt="AI Therapist" width="300" height="300">
  
  <h3>💙 Your Personal AI-Powered Mental Health Companion</h3>
  
  <p><strong>✨ Empowering mental wellness through intelligent voice, video & text conversations ✨</strong></p>
  
  <p>
    <a href="https://fastapi.tiangolo.com/"><img src="https://img.shields.io/badge/FastAPI-009688?style=for-the-badge&logo=fastapi&logoColor=white" alt="FastAPI"></a>
    <a href="https://python.org"><img src="https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white" alt="Python"></a>
    <a href="https://ai.google.dev/gemini-api"><img src="https://img.shields.io/badge/Gemini_2.0-4285F4?style=for-the-badge&logo=google&logoColor=white" alt="Gemini AI"></a>
    <a href="https://opensource.org/licenses/MIT"><img src="https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge" alt="License: MIT"></a>
  </p>
  
  <p>
    <a href="https://github.com/Omara-25/AI-Therapist-Gemini-integration/stargazers"><img src="https://img.shields.io/github/stars/Omara-25/AI-Therapist-Gemini-integration?style=for-the-badge&logo=github" alt="GitHub stars"></a>
    <a href="https://github.com/Omara-25/AI-Therapist-Gemini-integration/network"><img src="https://img.shields.io/github/forks/Omara-25/AI-Therapist-Gemini-integration?style=for-the-badge&logo=github" alt="GitHub forks"></a>
  </p>
  
  <p>
    <a href="#-quick-start">🚀 Get Started</a> • 
    <a href="#-revolutionary-features">✨ Features</a> • 
    <a href="#-how-to-use">📖 How to Use</a> • 
    <a href="#-for-developers">🛠️ For Developers</a> • 
    <a href="#-contributing">🤝 Contribute</a>
  </p>
</div>

---

## 🌟 About AI Therapist

**AI Therapist** is a revolutionary web-based mental health companion powered by **Google Gemini 2.0 Flash** and **Critical Future** innovation. This cutting-edge application offers real-time voice, video, and text therapeutic conversations with sub-250ms latency and multimodal AI capabilities.

**🎯 Perfect for:**
- Real-time voice therapy sessions with natural conversation flow
- Video therapy where AI can see and respond to your emotional cues  
- Document sharing and analysis with your AI Therapist
- Daily mental health check-ins and progress tracking
- Crisis intervention with immediate support protocols
- Stress management and personalized coping strategies

### 🌈 Why Choose Our AI Therapist?

| 🎤 Real-Time Voice | 📹 Video Interaction | 📄 Document Sharing |
|:---:|:---:|:---:|
| Natural conversations with 5 therapeutic voices | AI sees and responds to your visual cues | Upload files for AI analysis and discussion |

| 🔒 Privacy-First | ⚡ Sub-250ms Latency | 🆓 Open Source |
|:---:|:---:|:---:|
| End-to-end encryption, no data storage | Real-time responses with WebRTC | Fully open source and customizable |

---

## 🚀 Revolutionary Features

### 🎙️ Advanced Voice Therapy
- **🎤 Real-Time Voice Chat**: Natural conversations with sub-250ms latency using WebRTC
- **🔊 5 Therapeutic Voices**: Choose from Aoede, Kore, Charon, Puck, or Fenrir - each optimized for therapy
- **🎚️ Voice Controls**: Real-time mute/unmute, volume adjustment, and voice activity visualization
- **🔄 Natural Interruptions**: Seamless conversation flow with real-time audio processing
- **💫 Audio Visualization**: Live frequency analysis and interactive sound wave displays

### 📹 Groundbreaking Video Sessions  
- **👁️ AI Visual Recognition**: AI can see your facial expressions and body language for better support
- **📱 Your Video Only**: Privacy-focused design - only your video is visible, AI responds with voice
- **📄 File Upload Integration**: Share documents, images, PDFs directly in video sessions
- **🎯 Visual Cue Analysis**: AI provides insights based on non-verbal emotional indicators
- **🔧 Camera Controls**: Toggle camera on/off, adjust settings during sessions

### 📁 Document Sharing & Analysis
- **📤 Drag & Drop Upload**: Easy file sharing with visual upload zone
- **📄 Multi-Format Support**: Documents, images, PDFs, text files, and more
- **🧠 AI Document Analysis**: AI analyzes and discusses content therapeutically  
- **🗂️ File Management**: Visual file manager with remove/organize capabilities
- **💬 Therapeutic Discussion**: AI helps process and understand your shared materials

### 💬 Enhanced Text Chat
- **⌨️ Rich Text Conversations**: Traditional text-based therapy with full context memory
- **🎤 Speech-to-Text**: Voice input for text chat mode
- **📱 Mobile Responsive**: Perfect experience across all devices
- **🔄 Streaming Responses**: Real-time response generation for natural flow
- **📚 Session Memory**: AI remembers context throughout your conversation

### 🛡️ Advanced Session Management
- **👋 Natural Endings**: Say "bye" or "goodbye" to automatically end sessions gracefully
- **🔴 Manual Controls**: Clear start/end buttons with visual state indicators
- **⏰ Session Tracking**: Server-side session management and monitoring
- **🔐 Secure Cleanup**: Automatic cleanup of session data and WebRTC connections
- **📊 Real-Time Status**: Live connection status and session health monitoring

### 🎨 Professional UI/UX
- **🌙 Dark Mode Design**: Beautiful dark theme optimized for extended use
- **📱 Responsive Layout**: Perfect experience on desktop, tablet, and mobile
- **🎯 Mode Switching**: Seamless transitions between voice, video, and text modes
- **⚡ Real-Time Feedback**: Visual indicators for all system states
- **🎨 Modern Animations**: Smooth transitions and micro-interactions

---

## 🚀 Quick Start

### 🌐 Web Application (Recommended)

**🔗 Try it live:** [AI Therapist Web App](your-deployment-url)

**Or run locally:**

```bash
# 1️⃣ Clone the repository
git clone https://github.com/Omara-25/AI-Therapist.git

# 2️⃣ Navigate to project directory
cd AI-Therapist

# 3️⃣ Install dependencies
pip install -r requirements.txt

# 4️⃣ Set up environment variables
cp .env.example .env
# Edit .env with your Gemini API key

# 5️⃣ Run the application
python main.py
```

**🌍 Open your browser to:** `http://localhost:8000`

### 📋 Prerequisites
```
✅ Python 3.10+
✅ Gemini API Key (free from Google AI Studio)
✅ Modern web browser with WebRTC support
✅ Microphone and camera (for voice/video features)
```

---

## 📖 How to Use

### 🎤 Voice Therapy Sessions

1. **🌐 Open** the web application in your browser
2. **🎙️ Click** the "🎤 Voice Chat" tab
3. **🔊 Select** your preferred therapeutic voice (Aoede recommended)
4. **🎤 Click** "Start Voice Session" and allow microphone access
5. **🗣️ Speak** naturally - share your thoughts and feelings
6. **👂 Listen** to empathetic AI responses with real-time audio visualization
7. **👋 Say** "bye" or "goodbye" when ready to end, or click the end button

**🎛️ Voice Features:**
- Real-time mute/unmute toggle
- Live audio visualization
- Sub-250ms response latency
- Natural conversation interruptions

### 📹 Video Therapy with File Sharing

1. **📹 Switch** to "📹 Video Chat" tab
2. **📁 Upload** documents by dragging files to the upload zone
3. **🎤 Select** your therapeutic voice for the session
4. **📹 Click** "Start Video Session" and allow camera/microphone access
5. **👁️ Share** your screen - AI can see your expressions and body language
6. **📄 Discuss** uploaded files - AI analyzes and provides therapeutic insights
7. **🔧 Control** camera/microphone settings during the session
8. **👋 End** naturally by saying goodbye or using the end button

**📹 Video Features:**
- AI visual recognition of emotional cues
- Real-time file sharing and analysis
- Camera/microphone toggle controls
- Privacy-focused (no AI video response)

### 💬 Text Chat Experience

1. **💬 Click** the "💬 Text Chat" tab
2. **⌨️ Type** your thoughts in the text area
3. **🎤 Or use** the microphone button for speech-to-text
4. **📤 Send** your message and receive streaming AI responses
5. **📱 Continue** the conversation with full context memory
6. **👋 Say** "bye" in text to end the session gracefully

**💬 Text Features:**
- Streaming responses for natural flow
- Speech-to-text input option
- Auto-expanding text area
- Full conversation history

### 🎨 Customizing Your Experience

**🔊 Voice Settings:**
- Choose from 5 therapeutic voices
- Adjust for optimal emotional connection
- Each voice trained for therapeutic contexts

**📹 Video Preferences:**
- Toggle camera on/off during sessions
- Control microphone independently
- Manage uploaded files easily

**🎨 Interface Options:**
- Responsive design works on all devices
- Dark mode optimized for extended use
- Smooth animations and transitions

---

## 🛠️ For Developers

### 🏗️ Technology Stack

**Backend:**
- **FastAPI** - High-performance Python web framework
- **Google Gemini 2.0 Flash** - Multimodal AI with voice and vision
- **FastRTC** - WebRTC streaming for real-time communication
- **uvicorn** - ASGI server for production deployment

**Frontend:**
- **Modern HTML5/CSS3** - Responsive and accessible design
- **Vanilla JavaScript** - No framework dependencies, optimal performance
- **WebRTC APIs** - Real-time audio/video communication
- **File API** - Advanced file upload and management

**Infrastructure:**
- **Railway** - Cloud deployment platform
- **WebRTC/STUN/TURN** - Real-time communication infrastructure
- **End-to-end encryption** - Secure data transmission

### 📁 Project Structure
```
AI-Therapist-Gemini-integration/
├── 🐍 main.py                   # FastAPI application with WebRTC
├── 🌐 index.html                # Modern responsive web interface
├── 📋 requirements.txt          # Python dependencies
├── ⚙️ .env.example             # Environment configuration template
├── 🚀 railway.json             # Railway deployment configuration
├── 📖 README.md                # This comprehensive guide
└── 📄 LICENSE                  # MIT license
```

### 🔧 Key Components

**🎙️ Voice Processing:**
```python
class GeminiHandler(AsyncStreamHandler):
    # Real-time audio processing with Gemini 2.0 Flash
    # Sub-250ms latency optimization
    # Therapeutic voice selection and configuration
```

**📹 Video Analysis:**
```python  
class GeminiVideoHandler(AsyncAudioVideoStreamHandler):
    # Real-time video frame processing
    # AI visual recognition for therapy
    # File upload integration
```

**🌐 Web Interface:**
```javascript
// WebRTC management for real-time communication
// Modern UI with responsive design
// File upload with drag & drop
// Session management and cleanup
```

### 🚀 Development Setup

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Add your Gemini API key to .env

# Run in development mode
ENVIRONMENT=development python main.py
```

### 🌐 Deployment Options

**🚂 Railway (Recommended):**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway up
```

**🐳 Docker:**
```bash
# Build and run
docker build -t ai-Therapist .
docker run -p 8000:8000 --env-file .env ai-Therapist
```

**☁️ Other Platforms:**
- Heroku, Vercel, AWS, Google Cloud
- Configure port and environment variables
- Ensure WebRTC support

---

## 🤝 Contributing

We ❤️ contributions! Here's how you can help advance AI-powered mental health:

### 🌟 Ways to Contribute

| 🎤 Voice Features | 📹 Video Capabilities |
|:---:|:---:|
| Improve voice processing and latency | Enhance AI visual recognition |

| 🧠 AI Improvements | 🎨 UI/UX Design |
|:---:|:---:|
| Better therapeutic responses | Modern interface improvements |

| 🔒 Security & Privacy | 📱 Mobile Experience |
|:---:|:---:|
| Enhance encryption and privacy | Optimize mobile responsiveness |

### 📝 Development Process

1. **🍴 Fork** the repository
2. **🌿 Create** feature branch (`git checkout -b feature/amazing-improvement`)
3. **🔧 Make** your changes with proper testing
4. **📝 Add** documentation for new features
5. **💾 Commit** with clear messages (`git commit -m 'Add amazing improvement'`)
6. **⬆️ Push** to your branch (`git push origin feature/amazing-improvement`)
7. **🔄 Create** a detailed Pull Request

### 🧪 Testing Guidelines

- **🎤 Voice Features**: Test across different browsers and devices
- **📹 Video Processing**: Verify camera permissions and WebRTC compatibility  
- **📄 File Upload**: Test various file formats and sizes
- **🌐 Cross-Browser**: Ensure compatibility with Chrome, Firefox, Safari, Edge
- **📱 Mobile Testing**: Verify responsive design and touch interactions

---

## 🗺️ Roadmap

### 🔄 Version 2.2 (Current)
- ✅ **Gemini 2.0 Flash Integration** - Latest multimodal AI
- ✅ **Real-Time Voice Chat** - Sub-250ms latency
- ✅ **Video Therapy Sessions** - AI visual recognition
- ✅ **File Upload & Analysis** - Document sharing
- ✅ **Natural Session Endings** - Say goodbye to end
- ✅ **Critical Future Identity** - Proper AI attribution

### 🎯 Version 2.3 (Next Quarter)
- 🔄 **Multi-Language Support** - Global accessibility
- 🔄 **Session Persistence** - Resume conversations
- 🔄 **Advanced Analytics** - Therapy progress tracking
- 🔄 **Group Therapy Rooms** - Multi-user sessions
- 🔄 **Mobile App** - Native iOS/Android apps

### 🚀 Version 3.0 (Future)
- 📋 **EHR Integration** - Healthcare system connectivity
- 📋 **Provider Dashboard** - Professional therapist tools
- 📋 **AI-Assisted Diagnosis** - Advanced mental health insights
- 📋 **VR/AR Therapy** - Immersive therapy experiences
- 📋 **Biometric Integration** - Heart rate, stress monitoring

---

## ⚠️ Important Notice

**AI Therapist is a supportive tool created by Critical Future, not a replacement for professional mental health care.**

### 🚨 If You Need Immediate Help
- **🚨 Emergency**: Call your local emergency services
- **🆘 Crisis Support**: Contact a mental health crisis line  
- **👨‍⚕️ Professional Care**: Consult with licensed mental health professionals

### 📞 Crisis Resources
- **🌍 International**: [Befrienders Worldwide](https://www.befrienders.org/)
- **🇺🇸 United States**: National Suicide Prevention Lifeline: **988**
- **🇬🇧 United Kingdom**: Samaritans: **116 123**
- **🇨🇦 Canada**: Talk Suicide Canada: **1-**************

### 🛡️ Privacy & Data Protection

- **🔒 End-to-End Encryption**: All voice and video data encrypted in transit
- **🚫 No Data Storage**: Voice conversations not stored permanently  
- **🏠 Local Processing**: Maximum privacy with minimal data retention
- **👤 User Control**: Full control over your data and sessions

---

## 📊 Performance & Capabilities

### ⚡ Real-Time Performance
- **🎤 Voice Latency**: <250ms end-to-end
- **📹 Video Processing**: 30fps real-time analysis  
- **📁 File Upload**: Support for files up to 100MB
- **🌐 Concurrent Users**: Optimized for multiple simultaneous sessions
- **📱 Device Support**: Works on desktop, tablet, mobile

### 🧠 AI Capabilities
- **🗣️ Natural Language**: Context-aware therapeutic conversations
- **👁️ Visual Recognition**: Emotional cue detection and response
- **📄 Document Analysis**: Intelligent content analysis and discussion
- **🎯 Personalization**: Adaptive responses based on conversation history
- **🔄 Memory**: Maintains context throughout sessions

---

## 🛠️ API Documentation

### 🔗 Available Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Main application interface |
| `/health` | GET | System health and status |
| `/chat` | POST | Text-based chat API |
| `/ws` | WebSocket | Real-time text streaming |
| `/upload_file` | POST | File upload for AI analysis |
| `/audio/webrtc/offer` | POST | Voice chat WebRTC setup |
| `/video/webrtc/offer` | POST | Video chat WebRTC setup |
| `/api/voices` | GET | Available therapeutic voices |
| `/end_session` | POST | Manual session termination |

### 📱 Integration Examples

**🎤 Voice Chat Integration:**
```javascript
// Start voice session
const response = await fetch('/audio/webrtc/offer', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        sdp: localDescription.sdp,
        type: localDescription.type,
        webrtc_id: sessionId
    })
});
```

**📁 File Upload Integration:**
```javascript
// Upload file for AI analysis
const formData = new FormData();
formData.append('file', selectedFile);

const response = await fetch('/upload_file', {
    method: 'POST',
    body: formData
});
```

---

## 👥 Meet the Team

<div align="center">
  <h3>🚀 Critical Future Team</h3>
  <p><em>Building the future of AI-powered mental health technology</em></p>
  
  <table>
    <tr>
      <td align="center">
        <img src="https://github.com/Omara-25.png" width="100" style="border-radius: 50%;"/>
        <br>
        <strong>Omara-25</strong>
        <br>
        <em>Lead AI Engineer</em>
        <br>
        <em>Gemini Integration Specialist</em>
        <br>
        <a href="https://github.com/Omara-25">GitHub</a>
      </td>
    </tr>
  </table>
</div>

---

## 🙏 Acknowledgments

**Special thanks to the innovators and supporters who made this project possible:**

💙 **Google AI Team** - For Gemini 2.0 Flash multimodal capabilities  
🎤 **WebRTC Community** - For real-time communication standards  
🌐 **FastAPI Team** - For the high-performance web framework  
🧠 **Mental Health Advocates** - For inspiring better digital therapeutics  
🔒 **Privacy Advocates** - For ensuring user data protection  
⚡ **FastRTC Contributors** - For enabling real-time streaming  
👥 **Beta Testers** - For validating voice and video features  
❤️ **Our Users** - For trusting us with their mental health journey  

---

## 📞 Support & Contact

**Need help with the new features? We're here to support you!**

[![GitHub Issues](https://img.shields.io/badge/Bug_Reports-GitHub_Issues-red?style=for-the-badge&logo=github)](https://github.com/Omara-25/AI-Therapist-Gemini-integration/issues)
[![GitHub Discussions](https://img.shields.io/badge/Questions-GitHub_Discussions-blue?style=for-the-badge&logo=github)](https://github.com/Omara-25/AI-Therapist-Gemini-integration/discussions)

### 🆘 Technical Support
- 🐛 **Found a bug?** [Create an issue](https://github.com/Omara-25/AI-Therapist-Gemini-integration/issues)
- 💬 **Have questions?** [Start a discussion](https://github.com/Omara-25/AI-Therapist-Gemini-integration/discussions)  
- 🎤 **Voice chat issues?** Check our [Voice Troubleshooting Guide](https://github.com/Omara-25/AI-Therapist-Gemini-integration/wiki/Voice-Troubleshooting)
- 📹 **Video problems?** See our [Video Setup Guide](https://github.com/Omara-25/AI-Therapist-Gemini-integration/wiki/Video-Setup)
- 🚀 **Want to contribute?** Check our [Contributing Guidelines](#-contributing)

### 🌍 Community
- 💬 **Discord Server**: [Join our community](your-discord-link)
- 🐦 **Twitter Updates**: [@CriticalFuture](your-twitter-link)  
- 📧 **Email Support**: <EMAIL>

---

<div align="center">
  <h2>⭐ Star this repository if our AI Therapist helped you! ⭐</h2>
  
  <p><strong>Made with ❤️ by Critical Future for better mental health worldwide</strong></p>
  
  <p>
    <a href="https://github.com/Omara-25/Real-time-AI-Therapist"><img src="https://img.shields.io/badge/⭐_Star_on_GitHub-yellow?style=for-the-badge" alt="Star"></a>
    <a href="https://github.com/Omara-25/Real-time-AI-Therapist/fork"><img src="https://img.shields.io/badge/🍴_Fork_&_Contribute-blue?style=for-the-badge" alt="Fork"></a>
  </p>
  
  <h3>🎤 Real-Time Voice • 📹 Video Analysis • 📄 File Sharing • 🧠 Advanced AI</h3>
  
  <p><em>🌟 Thank you for supporting the future of AI-powered mental health care! 🌟</em></p>
  
  <p><strong>🚀 Powered by Critical Future </strong></p>