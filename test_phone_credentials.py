#!/usr/bin/env python3
"""
Test script for phone credentials extraction
"""
import sys
import re
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def extract_phone_credentials(text: str):
    """Extract phone credentials from text"""
    patterns = [
        r'Phone:\s*(\+?\d[\d\s\-\(\)]+).*?Access\s*[Cc]ode:\s*(\d+)',
        r'(\+\d[\d\s\-]+).*?(\d{6})',
        r'Call\s+(\+\d[\d\s\-]+).*?code\s+(\d+)',
        r'Number:\s*(\+\d[\d\s\-]+).*?Code:\s*(\d+)',
        r'FastPhone.*?(\+\d[\d\s\-]+).*?(\d{6})'
    ]
    
    for i, pattern in enumerate(patterns):
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            phone = match.group(1).strip()
            code = match.group(2).strip()
            print(f"✅ Pattern {i+1} matched: {phone} (Code: {code})")
            return phone, code
    
    return None, None

def test_extraction():
    """Test phone credentials extraction with sample data"""
    test_cases = [
        "Phone: ******-713-4471 Access Code: 163760",
        "FastPhone service started: ******-713-4471 with access code 163760",
        "Call ****** 713 4471 and use code 163760",
        "Number: ******-713-4471 Code: 163760",
        "******-713-4471 163760",
        "Your FastPhone number is ******-713-4471 and your access code is 163760"
    ]
    
    print("Testing phone credentials extraction...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case}")
        phone, code = extract_phone_credentials(test_case)
        if phone and code:
            print(f"✅ Extracted: {phone} (Code: {code})")
        else:
            print("❌ No credentials found")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_extraction()
    
    # Test with user input
    print("\nEnter a line containing phone credentials (or 'quit' to exit):")
    while True:
        try:
            line = input("> ")
            if line.lower() in ['quit', 'exit', 'q']:
                break
            
            phone, code = extract_phone_credentials(line)
            if phone and code:
                print(f"✅ Found: {phone} (Code: {code})")
            else:
                print("❌ No credentials found")
        except KeyboardInterrupt:
            break
    
    print("Goodbye!")
