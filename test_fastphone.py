#!/usr/bin/env python3
"""
Test FastPhone availability and methods
"""
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_fastphone():
    """Test FastPhone functionality"""
    print("Testing FastPhone availability...")
    
    try:
        from fastrtc import Stream
        print("✅ FastRTC Stream imported successfully")
        
        # Check if fastphone method exists
        if hasattr(Stream, 'fastphone'):
            print("✅ Stream.fastphone() method is available")
        else:
            print("❌ Stream.fastphone() method is NOT available")
            print("Available methods:")
            methods = [method for method in dir(Stream) if not method.startswith('_')]
            for method in methods:
                print(f"  - {method}")
        
        # Try to create a stream
        from backend.handlers.phone_handler import PhoneHandler
        
        stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=PhoneHandler(),
            concurrency_limit=10,
            time_limit=1800,
        )
        print("✅ Stream created successfully")
        
        # Check available methods on the instance
        print("\nAvailable methods on Stream instance:")
        instance_methods = [method for method in dir(stream) if not method.startswith('_') and callable(getattr(stream, method))]
        for method in instance_methods:
            print(f"  - {method}")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_alternative_phone_methods():
    """Test alternative phone methods"""
    print("\nTesting alternative phone methods...")
    
    try:
        from fastrtc import Stream
        
        # Check for phone-related methods
        phone_methods = []
        for attr in dir(Stream):
            if 'phone' in attr.lower():
                phone_methods.append(attr)
        
        if phone_methods:
            print("Found phone-related methods:")
            for method in phone_methods:
                print(f"  - {method}")
        else:
            print("No phone-related methods found")
            
        # Check for launch/start methods
        launch_methods = []
        for attr in dir(Stream):
            if any(keyword in attr.lower() for keyword in ['launch', 'start', 'run', 'serve']):
                launch_methods.append(attr)
        
        if launch_methods:
            print("Found launch/start methods:")
            for method in launch_methods:
                print(f"  - {method}")
        else:
            print("No launch/start methods found")
            
    except Exception as e:
        print(f"❌ Error testing alternative methods: {e}")

if __name__ == "__main__":
    success = test_fastphone()
    test_alternative_phone_methods()
    
    if not success:
        print("\n❌ FastPhone testing failed")
        print("💡 Suggestion: Check FastRTC documentation for phone functionality")
    else:
        print("\n✅ FastPhone testing completed")
